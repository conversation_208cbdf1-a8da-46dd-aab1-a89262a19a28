name: Security checks

on:
  push:
  pull_request:
    types: [synchronize, opened, reopened, ready_for_review]

# cancel previous workflow jobs for PRs
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.run_id }}
  cancel-in-progress: true

jobs:
  security-check:
    name: Security checks
    uses: dodopizza/security-action/.github/workflows/security-checks.yml@main
    secrets: inherit
