name: Build Frontend

on:
  workflow_dispatch:

jobs:
  build-frontend:
    name: Build Superset Frontend
    runs-on: ubuntu-latest
    env:
      NPM_TOKEN: ${{ secrets.PACKAGES_TOKEN }}

    steps:
      - name: Checkout dodopizza/superset
        uses: actions/checkout@v4.1.7
        with:
          path: repos/superset

      - name: Print branches versions
        run: |
          echo "Superset version ${{ github.ref }}"

      - name: Memory Check
        working-directory: repos/superset/docker
        run: |
          sh frontend-mem-nag.sh

      - name: Get image name from Superset
        id: image-name
        working-directory: repos/superset
        run: |
          BRANCH_NAME=$(git symbolic-ref -q --short HEAD || git describe --tags --exact-match)
          SHORT_SHA="$(git rev-parse --short HEAD)"
          IMAGE_NAME="${BRANCH_NAME}-${SHORT_SHA}-$(date +'%N')"
          echo "Image name: ${IMAGE_NAME}"
          echo "::set-output name=image_name::$IMAGE_NAME"

      - name: Build and push docker image with tag = ${{ steps.image-name.outputs.image_name }}
        uses: dodopizza/infra.github.push-action@latest
        with:
          context: repos/superset
          dockerfile: repos/superset/Dockerfile.DodoFrontend
          image_name: superset-frontend
          image_version: ${{ steps.image-name.outputs.image_name }}
          params: ${{ secrets.INFRA_GITHUB_PUSH_ACTION }}

      - name: Build and push docker image with tag = ${{ steps.image-name.outputs.image_name }}
        uses: dodopizza/infra.github.push-action@latest
        with:
          context: repos/superset
          dockerfile: repos/superset/Dockerfile.DodoFrontend
          image_name: superset-frontend
          image_version: latest
          params: ${{ secrets.INFRA_GITHUB_PUSH_ACTION }}
