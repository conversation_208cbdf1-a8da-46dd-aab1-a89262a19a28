# Stage 1: Build the frontend
FROM node:18.19.1-alpine as builder

WORKDIR /app

# Copy package files and install dependencies
# This leverages Docker cache. Dependencies are only re-installed when package.json or package-lock.json changes.
COPY superset-frontend/package.json superset-frontend/package-lock.json* ./superset-frontend/
RUN cd superset-frontend && npm ci

# Copy the rest of the frontend source code and build
# The build script presumably outputs to ../superset/static/assets
COPY superset-frontend/ ./superset-frontend/
RUN cd superset-frontend && npm run build

# Stage 2: Create the final image
FROM alpine

WORKDIR /app

# Copy only the built assets from the builder stage
COPY --from=builder /app/superset/static/assets/ /app/superset/static/assets/
