/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import { isLoadingMetricsEnabled } from './loadingMetrics';

// Mock window object
const mockWindow = (config: any) => {
  Object.defineProperty(window, 'bootstrapData', {
    writable: true,
    value: {
      common: {
        conf: config,
      },
    },
  });
};

describe('loadingMetrics', () => {
  beforeEach(() => {
    // Reset window.bootstrapData
    delete (window as any).bootstrapData;
  });

  describe('isLoadingMetricsEnabled', () => {
    it('should return false when DASHBOARD_LOADING_METRICS_ENABLED is false', () => {
      mockWindow({ DASHBOARD_LOADING_METRICS_ENABLED: false });
      expect(isLoadingMetricsEnabled()).toBe(false);
    });

    it('should return true when DASHBOARD_LOADING_METRICS_ENABLED is true and endpoint is configured', () => {
      mockWindow({
        DASHBOARD_LOADING_METRICS_ENABLED: true,
        DASHBOARD_LOADING_METRICS_ENDPOINT: '/api/v1/custom-metrics/',
      });
      expect(isLoadingMetricsEnabled()).toBe(true);
    });

    it('should return true when DASHBOARD_LOADING_METRICS_ENABLED is not set but endpoint is available', () => {
      mockWindow({
        DASHBOARD_LOADING_METRICS_ENDPOINT: '/api/v1/custom-metrics/',
      });
      expect(isLoadingMetricsEnabled()).toBe(true);
    });

    it('should return true when no config is provided (uses default endpoint)', () => {
      mockWindow({});
      expect(isLoadingMetricsEnabled()).toBe(true);
    });

    it('should return true when bootstrapData is not available (uses default endpoint)', () => {
      expect(isLoadingMetricsEnabled()).toBe(true);
    });
  });
});
