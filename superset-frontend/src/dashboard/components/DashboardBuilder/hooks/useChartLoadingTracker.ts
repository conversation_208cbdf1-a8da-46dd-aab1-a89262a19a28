/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import { useEffect, useRef } from 'react';
import { isLoadingMetricsEnabled } from '../utils/loadingMetrics';

export interface ChartLoadingMetrics {
  chartId: number;
  startTime: number;
  endTime?: number;
  loadTime?: number;
  status: 'loading' | 'rendered' | 'error';
  tabId?: string;
}

/**
 * Hook to track individual chart loading performance
 */
export const useChartLoadingTracker = (
  chartId: number,
  chartStatus: string,
  chartUpdateStartTime?: number,
  chartUpdateEndTime?: number,
  tabId?: string,
) => {
  const metricsRef = useRef<ChartLoadingMetrics | null>(null);
  const prevStatusRef = useRef<string>('');

  useEffect(() => {
    // Only track if metrics are enabled
    if (!isLoadingMetricsEnabled()) {
      return;
    }

    // Track when chart starts loading
    if (chartStatus === 'loading' && prevStatusRef.current !== 'loading') {
      const startTime = chartUpdateStartTime || Date.now();
      metricsRef.current = {
        chartId,
        startTime,
        status: 'loading',
        tabId,
      };

      // Dispatch custom event for dashboard-level tracking
      window.dispatchEvent(
        new CustomEvent('chart-loading-start', {
          detail: {
            chartId,
            startTime,
            tabId,
          },
        }),
      );
    }

    // Track when chart finishes loading (rendered or error)
    if (
      (chartStatus === 'rendered' || chartStatus === 'error') &&
      prevStatusRef.current === 'loading' &&
      metricsRef.current
    ) {
      const endTime = chartUpdateEndTime || Date.now();
      const loadTime = endTime - metricsRef.current.startTime;

      const finalMetrics: ChartLoadingMetrics = {
        ...metricsRef.current,
        endTime,
        loadTime,
        status: chartStatus as 'rendered' | 'error',
      };

      metricsRef.current = finalMetrics;

      // Dispatch custom event for dashboard-level tracking
      window.dispatchEvent(
        new CustomEvent('chart-loading-end', {
          detail: finalMetrics,
        }),
      );

      // Log individual chart metrics in development
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log(`Chart ${chartId} loading metrics:`, finalMetrics);
      }
    }

    prevStatusRef.current = chartStatus;
  }, [chartId, chartStatus, chartUpdateStartTime, chartUpdateEndTime, tabId]);

  // Cleanup on unmount
  useEffect(
    () => () => {
      if (metricsRef.current && !metricsRef.current.endTime) {
        // Chart was unmounted while loading, mark as cancelled
        const endTime = Date.now();
        const finalMetrics: ChartLoadingMetrics = {
          ...metricsRef.current,
          endTime,
          loadTime: endTime - metricsRef.current.startTime,
          status: 'error', // Treat unmount during loading as error
        };

        window.dispatchEvent(
          new CustomEvent('chart-loading-end', {
            detail: finalMetrics,
          }),
        );
      }
    },
    [],
  );

  return metricsRef.current;
};
