/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import { useCallback, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { ResourceStatus } from 'src/hooks/apiResources/apiResources';
import { RootState } from 'src/dashboard/types';
import { Chart, LayoutItem, DashboardLayout } from '../../../types';
import { CHART_TYPE, TAB_TYPE } from '../../../util/componentTypes';
import {
  sendLoadingMetrics,
  isLoadingMetricsEnabled,
} from '../utils/loadingMetrics';

interface TabChart {
  sliceId: number;
  startLoadTime: number;
  endLoadTime: number;
  hasError: boolean;
}

interface Tab {
  startLoadTime: number;
  endLoadTime: number;
  hasError: boolean;
  charts: Record<number, TabChart>;
}

export interface DashboardPerformance {
  dashboardIdOrSlug: string;
  hasError: boolean;
  startLoadTime: number;
  dashboardMtdtEndLoadTime: number;
  chartsMtdtEndLoadTime: number;
  datasetsEndLoadTime: number;
  filtersetsEndLoadTime: number;
  tabs: Record<string, Tab>;
}

interface ChartLoadingInfo {
  chartId: number;
  startTime: number;
  endTime?: number;
  tabId?: string;
}

export const useDashboardLoadingTracker = (
  idOrSlug: string,
  apiStatuses: {
    dashboard: ResourceStatus;
    charts: ResourceStatus;
    datasets: ResourceStatus;
    filtersets: ResourceStatus;
  },
) => {
  const dashboardPerformanceRef = useRef<DashboardPerformance>({
    dashboardIdOrSlug: idOrSlug,
    hasError: false,
    startLoadTime: Date.now(),
    dashboardMtdtEndLoadTime: 0,
    chartsMtdtEndLoadTime: 0,
    datasetsEndLoadTime: 0,
    filtersetsEndLoadTime: 0,
    tabs: {},
  });

  // Track charts loading info
  const chartsLoadingRef = useRef<ChartLoadingInfo[]>([]);

  // Track visited tabs to only include active tabs in metrics
  const visitedTabsRef = useRef<Set<string>>(new Set());

  const charts = useSelector((state: RootState) => state.charts);
  const activeTabs = useSelector(
    (state: RootState) => state.dashboardState?.activeTabs || [],
  );
  const currentTabId = useSelector(
    (state: RootState) => state.dashboardState?.currentTabId,
  );
  console.log('currentTabId', currentTabId);
  const dashboardLayout = useSelector(
    (state: RootState) => state.dashboardLayout?.present || {},
  ) as DashboardLayout;

  // Function to get chart IDs that are visible on active tabs
  const getVisibleChartIds = useCallback(() => {
    const allChartIds = Object.values(charts).map((chart: Chart) => chart.id);

    if (activeTabs.length === 0) {
      // If no active tabs, all charts are visible
      return allChartIds;
    }

    return allChartIds.filter(chartId => {
      // Find the chart's layout item
      const chartLayoutItem = Object.values(dashboardLayout).find(
        (layoutItem: LayoutItem) =>
          layoutItem?.type === CHART_TYPE &&
          layoutItem.meta?.chartId === chartId,
      );

      if (!chartLayoutItem) return false;

      // Get all tab parents of this chart
      const tabParents =
        chartLayoutItem.parents?.filter(
          (parentId: string) => dashboardLayout[parentId]?.type === TAB_TYPE,
        ) || [];

      // Chart is visible if:
      // 1. It has no tab parents (not in any tab)
      // 2. All of its tab parents are active
      return (
        tabParents.length === 0 ||
        tabParents.every((tabId: string) => activeTabs.includes(tabId))
      );
    });
  }, [charts, activeTabs, dashboardLayout]);

  const visibleChartIds = getVisibleChartIds();
  const renderedChartIds = useSelector((state: RootState) =>
    Object.values(state.charts)
      .filter((chart: Chart) => chart.chartStatus === 'rendered')
      .map((chart: Chart) => chart.id),
  );

  const prevRenderedChartIds = useRef<number[]>([]);
  const prevTabId = useRef<string | undefined>(currentTabId);
  const hasInitialized = useRef<boolean>(false);

  // Check if dashboard loading is complete (only for visible charts)
  // const isDashboardLoaded = useCallback(() => {
  //   if (visibleChartIds.length === 0) return true; // No charts to load

  //   // All visible charts should be rendered
  //   const allVisibleChartsRendered = visibleChartIds.every(chartId =>
  //     renderedChartIds.includes(chartId),
  //   );

  //   // All tracked visible charts should have end times
  //   const trackedVisibleCharts = chartsLoadingRef.current.filter(
  //     (chart: ChartLoadingInfo) => visibleChartIds.includes(chart.chartId),
  //   );

  //   // If no charts are being tracked yet, dashboard is not loaded
  //   if (trackedVisibleCharts.length === 0) return false;

  //   const allTrackedVisibleChartsComplete = trackedVisibleCharts.every(
  //     (chart: ChartLoadingInfo) => chart.endTime,
  //   );

  //   return allVisibleChartsRendered && allTrackedVisibleChartsComplete;
  // }, [visibleChartIds, renderedChartIds]);

  // Track API loading completion
  useEffect(() => {
    const currentTime = Date.now();

    // Track dashboard metadata completion
    if (
      apiStatuses.dashboard === ResourceStatus.Complete &&
      !dashboardPerformanceRef.current.dashboardMtdtEndLoadTime
    ) {
      dashboardPerformanceRef.current.dashboardMtdtEndLoadTime = currentTime;
    }

    // Track charts metadata completion
    if (
      apiStatuses.charts === ResourceStatus.Complete &&
      !dashboardPerformanceRef.current.chartsMtdtEndLoadTime
    ) {
      dashboardPerformanceRef.current.chartsMtdtEndLoadTime = currentTime;
    }

    // Track datasets completion
    if (
      apiStatuses.datasets === ResourceStatus.Complete &&
      !dashboardPerformanceRef.current.datasetsEndLoadTime
    ) {
      dashboardPerformanceRef.current.datasetsEndLoadTime = currentTime;
    }

    // Track filtersets completion
    if (
      apiStatuses.filtersets === ResourceStatus.Complete &&
      !dashboardPerformanceRef.current.filtersetsEndLoadTime
    ) {
      dashboardPerformanceRef.current.filtersetsEndLoadTime = currentTime;
    }

    // Track errors
    if (
      apiStatuses.dashboard === ResourceStatus.Error ||
      apiStatuses.charts === ResourceStatus.Error ||
      apiStatuses.datasets === ResourceStatus.Error ||
      apiStatuses.filtersets === ResourceStatus.Error
    ) {
      dashboardPerformanceRef.current.hasError = true;
    }
  }, [apiStatuses]);

  // Track active tabs and add them to visited tabs
  useEffect(() => {
    activeTabs.forEach((tabId: string) => {
      visitedTabsRef.current.add(tabId);
    });

    // Add current tab if it exists
    if (currentTabId) {
      visitedTabsRef.current.add(currentTabId);
    }
  }, [activeTabs, currentTabId]);

  // Listen to chart loading events
  useEffect(() => {
    const handleChartLoadingStart = (event: CustomEvent) => {
      const { chartId, startTime, tabId } = event.detail;

      // Only track charts that are visible
      if (!visibleChartIds.includes(chartId)) return;

      const existingChart = chartsLoadingRef.current.find(
        c => c.chartId === chartId,
      );
      if (existingChart) return;

      chartsLoadingRef.current.push({
        chartId,
        startTime,
        tabId,
      });
    };

    const handleChartLoadingEnd = (event: CustomEvent) => {
      const { chartId, endTime } = event.detail;

      // Only track charts that are visible
      if (!visibleChartIds.includes(chartId)) return;

      const chartIndex = chartsLoadingRef.current.findIndex(
        c => c.chartId === chartId,
      );
      if (chartIndex !== -1 && !chartsLoadingRef.current[chartIndex].endTime) {
        chartsLoadingRef.current[chartIndex].endTime = endTime;
      }
    };

    window.addEventListener(
      'chart-loading-start',
      handleChartLoadingStart as EventListener,
    );
    window.addEventListener(
      'chart-loading-end',
      handleChartLoadingEnd as EventListener,
    );

    return () => {
      window.removeEventListener(
        'chart-loading-start',
        handleChartLoadingStart as EventListener,
      );
      window.removeEventListener(
        'chart-loading-end',
        handleChartLoadingEnd as EventListener,
      );
    };
  }, [visibleChartIds]);

  // Track tab switches and initialize tab data
  useEffect(() => {
    if (prevTabId.current !== currentTabId && hasInitialized.current) {
      // Add new tab to visited tabs
      if (currentTabId) {
        visitedTabsRef.current.add(currentTabId);

        // Initialize tab in performance data if not exists
        if (!dashboardPerformanceRef.current.tabs[currentTabId]) {
          dashboardPerformanceRef.current.tabs[currentTabId] = {
            startLoadTime: Date.now(),
            endLoadTime: 0,
            hasError: false,
            charts: {},
          };
        }
      }

      prevTabId.current = currentTabId;
    }
  }, [currentTabId]);

  // Track chart loading states (only for visible charts)
  useEffect(() => {
    Object.values(charts).forEach((chart: Chart) => {
      // Only track visible charts
      if (!visibleChartIds.includes(chart.id)) return;

      const existingChart = chartsLoadingRef.current.find(
        c => c.chartId === chart.id,
      );

      if (chart.chartStatus === 'loading' && !existingChart) {
        // Chart started loading
        chartsLoadingRef.current.push({
          chartId: chart.id,
          startTime: chart.chartUpdateStartTime || Date.now(),
          tabId: currentTabId,
        });
      } else if (
        chart.chartStatus === 'rendered' &&
        existingChart &&
        !existingChart.endTime
      ) {
        // Chart finished loading
        existingChart.endTime = chart.chartUpdateEndTime || Date.now();

        // Update tab data with chart info
        if (
          currentTabId &&
          dashboardPerformanceRef.current.tabs[currentTabId]
        ) {
          dashboardPerformanceRef.current.tabs[currentTabId].charts[chart.id] =
            {
              sliceId: chart.id,
              startLoadTime: existingChart.startTime,
              endLoadTime: existingChart.endTime,
              hasError: false,
            };
        }
      } else if (
        chart.chartStatus === 'failed' &&
        existingChart &&
        !existingChart.endTime
      ) {
        // Chart failed loading
        existingChart.endTime = Date.now();
        dashboardPerformanceRef.current.hasError = true;

        // Update tab data with error info
        if (
          currentTabId &&
          dashboardPerformanceRef.current.tabs[currentTabId]
        ) {
          dashboardPerformanceRef.current.tabs[currentTabId].charts[chart.id] =
            {
              sliceId: chart.id,
              startLoadTime: existingChart.startTime,
              endLoadTime: existingChart.endTime,
              hasError: true,
            };
          dashboardPerformanceRef.current.tabs[currentTabId].hasError = true;
        }
      }
    });
  }, [charts, currentTabId, visibleChartIds]);

  // Track when new charts are rendered
  useEffect(() => {
    const newlyRenderedCharts = renderedChartIds.filter(
      id => !prevRenderedChartIds.current.includes(id),
    );

    if (newlyRenderedCharts.length > 0) {
      prevRenderedChartIds.current = renderedChartIds;
    }
  }, [renderedChartIds]);

  // Send metrics when dashboard unloads
  useEffect(() => {
    const handleUnload = () => {
      console.log('beforeunload', dashboardPerformanceRef.current);
      // sendLoadingMetrics(dashboardPerformanceRef.current);
    };

    window.addEventListener('beforeunload', handleUnload);
    return () => {
      window.removeEventListener('beforeunload', handleUnload);
    };
  }, []);
};
