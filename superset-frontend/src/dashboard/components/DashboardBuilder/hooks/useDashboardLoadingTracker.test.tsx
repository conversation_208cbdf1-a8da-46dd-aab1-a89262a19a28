/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
import React from 'react';
import { renderHook } from '@testing-library/react-hooks';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import { useDashboardLoadingTracker } from './useDashboardLoadingTracker';
import { CHART_TYPE, TAB_TYPE } from '../../../util/componentTypes';

// Mock the loadingMetrics module
jest.mock('../utils/loadingMetrics', () => ({
  isLoadingMetricsEnabled: jest.fn(() => true),
  sendLoadingMetrics: jest.fn(() => Promise.resolve()),
}));

const createMockStore = (state: any) => createStore(() => state);

const mockCharts = {
  1: { id: 1, chartStatus: 'rendered' },
  2: { id: 2, chartStatus: 'rendered' },
  3: { id: 3, chartStatus: 'loading' },
};

const mockDashboardLayout = {
  'CHART-1': {
    id: 'CHART-1',
    type: CHART_TYPE,
    meta: { chartId: 1 },
    parents: ['ROOT_ID', 'TAB-1'],
    children: [],
  },
  'CHART-2': {
    id: 'CHART-2',
    type: CHART_TYPE,
    meta: { chartId: 2 },
    parents: ['ROOT_ID', 'TAB-2'],
    children: [],
  },
  'CHART-3': {
    id: 'CHART-3',
    type: CHART_TYPE,
    meta: { chartId: 3 },
    parents: ['ROOT_ID'],
    children: [],
  },
  'TAB-1': {
    id: 'TAB-1',
    type: TAB_TYPE,
    parents: ['ROOT_ID'],
    children: ['CHART-1'],
  },
  'TAB-2': {
    id: 'TAB-2',
    type: TAB_TYPE,
    parents: ['ROOT_ID'],
    children: ['CHART-2'],
  },
};

describe('useDashboardLoadingTracker', () => {
  it('should only consider visible charts when determining if dashboard is loaded', () => {
    const mockState = {
      charts: mockCharts,
      dashboardState: {
        activeTabs: ['TAB-1'], // Only TAB-1 is active
      },
      dashboardLayout: {
        present: mockDashboardLayout,
      },
    };

    const store = createMockStore(mockState);
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>{children}</Provider>
    );

    const { result } = renderHook(() => useDashboardLoadingTracker(1, 'tab1'), {
      wrapper,
    });

    // Should return false because chart 3 (not in any tab) is still loading
    // Chart 2 is in TAB-2 which is not active, so it should be ignored
    expect(result.current.isDashboardLoaded).toBe(false);
  });

  it('should consider all charts when no tabs are active', () => {
    const mockState = {
      charts: {
        1: { id: 1, chartStatus: 'rendered' },
        2: { id: 2, chartStatus: 'rendered' },
      },
      dashboardState: {
        activeTabs: [], // No active tabs
      },
      dashboardLayout: {
        present: mockDashboardLayout,
      },
    };

    const store = createMockStore(mockState);
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>{children}</Provider>
    );

    const { result } = renderHook(() => useDashboardLoadingTracker(1, 'tab1'), {
      wrapper,
    });

    // Should return false because we have no loading info for charts
    expect(result.current.isDashboardLoaded).toBe(false);
  });

  it('should consider charts without tab parents as always visible', () => {
    const mockState = {
      charts: {
        3: { id: 3, chartStatus: 'rendered' },
      },
      dashboardState: {
        activeTabs: ['TAB-1'], // TAB-1 is active but chart 3 is not in any tab
      },
      dashboardLayout: {
        present: mockDashboardLayout,
      },
    };

    const store = createMockStore(mockState);
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>{children}</Provider>
    );

    const { result } = renderHook(() => useDashboardLoadingTracker(1, 'tab1'), {
      wrapper,
    });

    // Chart 3 has no tab parents, so it should be considered visible
    expect(result.current.isDashboardLoaded).toBe(false); // Still false due to no loading info
  });
});
