# dodo added 44120742

import sqlalchemy as sa

from superset.common.query_context import Query<PERSON><PERSON>xt


def translate_chart_to_russian(query_context: QueryContext) -> None:
    """
    Temporarily translate column and metric verbose names to Russian.

    This function temporarily disables SQLAlchemy event listeners to prevent
    automatic database updates when modifying verbose_name properties.
    """
    from superset.connectors.sqla.models import SqlaTable, SqlMetric, TableColumn

    # Store original event listeners to restore them later
    original_listeners = []

    # Temporarily remove event listeners that trigger database updates
    try:
        # Remove the after_update listeners for TableColumn and SqlMetric
        for target_class in [TableColumn, SqlMetric]:
            listeners = sa.event.contains(
                target_class, "after_update", SqlaTable.update_column
            )
            if listeners:
                sa.event.remove(target_class, "after_update", SqlaTable.update_column)
                original_listeners.append(
                    (target_class, "after_update", SqlaTable.update_column)
                )

        # Perform the translation without triggering database updates
        for column in query_context.datasource.columns:
            if column.verbose_name_ru:
                # Store original verbose_name in verbose_name_en if not already set
                if not column.verbose_name_en:
                    column.verbose_name_en = column.verbose_name
                column.verbose_name = column.verbose_name_ru

        for metric in query_context.datasource.metrics:
            if metric.verbose_name_ru:
                # Store original verbose_name in verbose_name_en if not already set
                if not metric.verbose_name_en:
                    metric.verbose_name_en = metric.verbose_name
                metric.verbose_name = metric.verbose_name_ru

    finally:
        # Restore the original event listeners
        for target_class, event_name, listener in original_listeners:
            sa.event.listen(target_class, event_name, listener)


def revert_translate(query_context: QueryContext) -> None:
    """
    Revert the temporary Russian translation back to original values.

    This function also temporarily disables SQLAlchemy event listeners to prevent
    automatic database updates when reverting verbose_name properties.
    """
    from superset.connectors.sqla.models import SqlaTable, SqlMetric, TableColumn

    # Store original event listeners to restore them later
    original_listeners = []

    # Temporarily remove event listeners that trigger database updates
    try:
        # Remove the after_update listeners for TableColumn and SqlMetric
        for target_class in [TableColumn, SqlMetric]:
            listeners = sa.event.contains(
                target_class, "after_update", SqlaTable.update_column
            )
            if listeners:
                sa.event.remove(target_class, "after_update", SqlaTable.update_column)
                original_listeners.append(
                    (target_class, "after_update", SqlaTable.update_column)
                )

        # Revert the translation without triggering database updates
        for column in query_context.datasource.columns:
            if (
                hasattr(column, "verbose_name_en")
                and column.verbose_name_en is not None
            ):
                column.verbose_name = column.verbose_name_en

        for metric in query_context.datasource.metrics:
            if (
                hasattr(metric, "verbose_name_en")
                and metric.verbose_name_en is not None
            ):
                metric.verbose_name = metric.verbose_name_en

    finally:
        # Restore the original event listeners
        for target_class, event_name, listener in original_listeners:
            sa.event.listen(target_class, event_name, listener)
