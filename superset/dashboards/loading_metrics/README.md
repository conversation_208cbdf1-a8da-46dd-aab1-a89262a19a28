# Dashboard Loading Metrics

Система отслеживания времени загрузки дашбордов и графиков в Apache Superset.

## Описание

Эта система автоматически отслеживает:
- Время загрузки всего дашборда
- Время загрузки каждого отдельного графика
- Переключения между вкладками дашборда
- Метаданные браузера и пользователя

## Конфигурация

### Backend конфигурация

В файле `superset/config.py` или `superset_config.py`:

```python
# Включить/выключить отслеживание метрик загрузки
DASHBOARD_LOADING_METRICS_ENABLED = True

# URL endpoint для отправки метрик (по умолчанию: /api/v1/dashboard_loading_metrics/)
DASHBOARD_LOADING_METRICS_ENDPOINT = "/api/v1/dashboard/loading-metrics"
```

### Frontend конфигурация

Система автоматически получает конфигурацию через bootstrap data. Никаких дополнительных настроек на frontend не требуется.

## Архитектура

### Frontend компоненты

1. **useDashboardLoadingTracker** - React hook для отслеживания загрузки дашборда
2. **useChartLoadingTracker** - React hook для отслеживания загрузки отдельных графиков
3. **loadingMetrics.ts** - Утилиты для отправки метрик на backend

### Backend компоненты

1. **DashboardLoadingMetricsApi** - REST API для получения метрик
2. **Схемы валидации** - Marshmallow схемы для валидации данных

## Структура данных

### Метрики дашборда

```typescript
interface DashboardLoadingMetrics {
  dashboardId: number;
  totalLoadTime: number;
  dashboardStartTime: number;
  dashboardEndTime: number;
  charts: ChartLoadingMetrics[];
  tabSwitches: TabSwitch[];
  metadata: LoadingMetricsMetadata;
}
```

### Метрики графика

```typescript
interface ChartLoadingMetrics {
  chartId: number;
  loadTime: number;
  startTime: number;
  endTime: number;
  tabId?: string;
}
```

### Переключения вкладок

```typescript
interface TabSwitch {
  fromTab?: string;
  toTab: string;
  timestamp: number;
}
```

## Использование

### Автоматическое отслеживание

Система работает автоматически после включения в конфигурации. Метрики собираются и отправляются без вмешательства пользователя.

### Ручная отправка метрик

```typescript
import { sendLoadingMetrics } from 'src/dashboard/components/DashboardBuilder/utils/loadingMetrics';

// Отправить метрики вручную
await sendLoadingMetrics(loadingInfo);
```

### Повторная отправка неудачных метрик

```typescript
import { retryFailedMetrics } from 'src/dashboard/components/DashboardBuilder/utils/loadingMetrics';

// Повторить отправку неудачных метрик
await retryFailedMetrics();
```

## Логирование

Система логирует:
- Успешную отправку метрик (в development режиме)
- Ошибки отправки метрик
- Индивидуальные метрики графиков (в development режиме)

## Хранение данных

- Неудачные метрики временно сохраняются в localStorage
- Автоматическая повторная отправка при следующем посещении
- Ограничение: максимум 10 неудачных метрик в localStorage

## API Endpoint

### POST /api/v1/dashboard_loading_metrics/

Принимает JSON с метриками загрузки дашборда.

**Пример запроса:**

```json
{
  "dashboardId": 123,
  "totalLoadTime": 2500,
  "dashboardStartTime": 1640995200000,
  "dashboardEndTime": 1640995202500,
  "charts": [
    {
      "chartId": 456,
      "loadTime": 1200,
      "startTime": 1640995200500,
      "endTime": 1640995201700,
      "tabId": "tab-1"
    }
  ],
  "tabSwitches": [
    {
      "fromTab": "tab-1",
      "toTab": "tab-2",
      "timestamp": 1640995203000
    }
  ],
  "metadata": {
    "userAgent": "Mozilla/5.0...",
    "url": "http://localhost:8088/superset/dashboard/123/",
    "timestamp": 1640995202500,
    "totalCharts": 5,
    "chartsPerTab": {
      "tab-1": 3,
      "tab-2": 2
    }
  }
}
```

**Ответ:**

```json
{
  "message": "Loading metrics received successfully"
}
```

## Отключение системы

Для отключения системы установите в конфигурации:

```python
DASHBOARD_LOADING_METRICS_ENABLED = False
```

Или удалите/закомментируйте конфигурационные параметры.
