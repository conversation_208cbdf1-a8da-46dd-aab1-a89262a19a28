# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

import logging
from datetime import datetime
from typing import Any, Dict

from flask import request
from flask_appbuilder.api import BaseApi, expose, safe
from flask_appbuilder.security.decorators import has_access_api
from marshmallow import fields, Schema, ValidationError

from superset.constants import MODEL_API_RW_METHOD_PERMISSION_MAP
from superset.extensions import event_logger

logger = logging.getLogger(__name__)


class ChartLoadingMetricsSchema(Schema):
    chartId = fields.Integer(required=True)
    loadTime = fields.Integer(required=True)
    startTime = fields.Integer(required=True)
    endTime = fields.Integer(required=True)
    tabId = fields.String(allow_none=True)


class TabSwitchSchema(Schema):
    fromTab = fields.String(allow_none=True)
    toTab = fields.String(required=True)
    timestamp = fields.Integer(required=True)


class LoadingMetricsMetadataSchema(Schema):
    userAgent = fields.String(required=True)
    url = fields.String(required=True)
    timestamp = fields.Integer(required=True)
    totalCharts = fields.Integer(required=True)
    chartsPerTab = fields.Dict(keys=fields.String(), values=fields.Integer())


class DashboardLoadingMetricsSchema(Schema):
    dashboardId = fields.Integer(required=True)
    totalLoadTime = fields.Integer(required=True)
    dashboardStartTime = fields.Integer(required=True)
    dashboardEndTime = fields.Integer(required=True)
    charts = fields.List(fields.Nested(ChartLoadingMetricsSchema), required=True)
    tabSwitches = fields.List(fields.Nested(TabSwitchSchema), required=True)
    metadata = fields.Nested(LoadingMetricsMetadataSchema, required=True)


class DashboardLoadingMetricsApi(BaseApi):
    resource_name = "dashboard_loading_metrics"
    allow_browser_login = True
    class_permission_name = "Dashboard"
    method_permission_name = MODEL_API_RW_METHOD_PERMISSION_MAP

    @expose("/", methods=["POST"])
    @has_access_api
    @safe
    def post(self) -> Dict[str, Any]:
        """
        Receive and log dashboard loading metrics
        ---
        post:
          summary: Submit dashboard loading metrics
          requestBody:
            description: Dashboard loading metrics data
            required: true
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/DashboardLoadingMetricsSchema'
          responses:
            200:
              description: Metrics received successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      message:
                        type: string
            400:
              $ref: '#/components/responses/400'
            401:
              $ref: '#/components/responses/401'
            500:
              $ref: '#/components/responses/500'
        """
        try:
            # Validate the incoming data
            schema = DashboardLoadingMetricsSchema()
            metrics_data = schema.load(request.json)
            
            # Log the metrics for analysis
            self._log_loading_metrics(metrics_data)
            
            # Store metrics in database if needed
            # self._store_metrics(metrics_data)
            
            return {"message": "Loading metrics received successfully"}, 200
            
        except ValidationError as e:
            logger.error(f"Invalid loading metrics data: {e.messages}")
            return {"message": "Invalid metrics data", "errors": e.messages}, 400
        except Exception as e:
            logger.error(f"Error processing loading metrics: {str(e)}")
            return {"message": "Internal server error"}, 500

    def _log_loading_metrics(self, metrics_data: Dict[str, Any]) -> None:
        """Log loading metrics for analysis"""
        try:
            dashboard_id = metrics_data["dashboardId"]
            total_load_time = metrics_data["totalLoadTime"]
            chart_count = len(metrics_data["charts"])
            
            # Log to event logger
            event_logger.log_with_context(
                action="dashboard_loading_metrics",
                dashboard_id=dashboard_id,
                total_load_time_ms=total_load_time,
                chart_count=chart_count,
                tab_switches=len(metrics_data["tabSwitches"]),
                user_agent=metrics_data["metadata"]["userAgent"],
                url=metrics_data["metadata"]["url"],
                timestamp=datetime.fromtimestamp(metrics_data["metadata"]["timestamp"] / 1000),
            )
            
            # Log individual chart metrics
            for chart_metric in metrics_data["charts"]:
                event_logger.log_with_context(
                    action="chart_loading_metrics",
                    dashboard_id=dashboard_id,
                    chart_id=chart_metric["chartId"],
                    load_time_ms=chart_metric["loadTime"],
                    tab_id=chart_metric.get("tabId"),
                    start_time=datetime.fromtimestamp(chart_metric["startTime"] / 1000),
                    end_time=datetime.fromtimestamp(chart_metric["endTime"] / 1000),
                )
            
            logger.info(
                f"Dashboard {dashboard_id} loading metrics: "
                f"total_time={total_load_time}ms, charts={chart_count}, "
                f"tab_switches={len(metrics_data['tabSwitches'])}"
            )
            
        except Exception as e:
            logger.error(f"Error logging loading metrics: {str(e)}")

    def _store_metrics(self, metrics_data: Dict[str, Any]) -> None:
        """Store metrics in database for future analysis (optional)"""
        # This method can be implemented to store metrics in a dedicated table
        # for more detailed analysis and reporting
        pass
