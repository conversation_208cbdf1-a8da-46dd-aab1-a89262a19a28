# This file has been auto-generated. Do not edit manually.
# If you would like to contribute new rules, please use
# cmd/generate/config/main.go and follow the contributing guidelines
# at https://github.com/zricethezav/gitleaks/blob/master/CONTRIBUTING.md

# This is the default gitleaks configuration file.
# Rules and allowlists are defined within this file.
# Rules instruct gitleaks on what should be considered a secret.
# Allowlists instruct gitleaks on what is allowed, i.e. not a secret.

title = "gitleaks config"

[allowlist]
description = "global allow lists"
paths = [
    '''gitleaks.toml''',
    '''(.*?)(jpg|gif|doc|docx|zip|xls|pdf|bin|svg|socket|vsidx|v2|suo|wsuo|.dll|pdb|exe)$''',
    '''(go.mod|go.sum)$''',
    '''gradle.lockfile''',
    '''node_modules''',
    '''package-lock.json''',
    '''yarn.lock''',
    '''pnpm-lock.yaml''',
    '''Database.refactorlog''',
    '''vendor''',
]

[[rules]]
id = "adafruit-api-key"
description = "Identified a potential Adafruit API Key, which could lead to unauthorized access to Adafruit services and sensitive data exposure."
regex = '''(?i)(?:adafruit)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9_-]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "adafruit",
]

[[rules]]
id = "adobe-client-id"
description = "Detected a pattern that resembles an Adobe OAuth Web Client ID, posing a risk of compromised Adobe integrations and data breaches."
regex = '''(?i)(?:adobe)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "adobe",
]

[[rules]]
id = "adobe-client-secret"
description = "Discovered a potential Adobe Client Secret, which, if exposed, could allow unauthorized Adobe service access and data manipulation."
regex = '''(?i)\b((p8e-)(?i)[a-z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "p8e-",
]

[[rules]]
id = "age secret key"
description = "Discovered a potential Age encryption tool secret key, risking data decryption and unauthorized access to sensitive information."
regex = '''AGE-SECRET-KEY-1[QPZRY9X8GF2TVDW0S3JN54KHCE6MUA7L]{58}'''
keywords = [
    "age-secret-key-1",
]

[[rules]]
id = "airtable-api-key"
description = "Uncovered a possible Airtable API Key, potentially compromising database access and leading to data leakage or alteration."
regex = '''(?i)(?:airtable)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{17})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "airtable",
]

[[rules]]
id = "algolia-api-key"
description = "Identified an Algolia API Key, which could result in unauthorized search operations and data exposure on Algolia-managed platforms."
regex = '''(?i)(?:algolia)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "algolia",
]

[[rules]]
id = "alibaba-access-key-id"
description = "Detected an Alibaba Cloud AccessKey ID, posing a risk of unauthorized cloud resource access and potential data compromise."
regex = '''(?i)\b((LTAI)(?i)[a-z0-9]{20})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "ltai",
]

[[rules]]
id = "alibaba-secret-key"
description = "Discovered a potential Alibaba Cloud Secret Key, potentially allowing unauthorized operations and data access within Alibaba Cloud."
regex = '''(?i)(?:alibaba)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{30})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "alibaba",
]

[[rules]]
id = "asana-client-id"
description = "Discovered a potential Asana Client ID, risking unauthorized access to Asana projects and sensitive task information."
regex = '''(?i)(?:asana)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9]{16})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "asana",
]

[[rules]]
id = "asana-client-secret"
description = "Identified an Asana Client Secret, which could lead to compromised project management integrity and unauthorized access."
regex = '''(?i)(?:asana)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "asana",
]

[[rules]]
id = "atlassian-api-token"
description = "Detected an Atlassian API token, posing a threat to project management and collaboration tool security and data confidentiality."
regex = '''(?i)(?:atlassian|confluence|jira)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{24})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "atlassian","confluence","jira",
]

[[rules]]
id = "authress-service-client-access-key"
description = "Uncovered a possible Authress Service Client Access Key, which may compromise access control services and sensitive data."
regex = '''(?i)\b((?:sc|ext|scauth|authress)_[a-z0-9]{5,30}\.[a-z0-9]{4,6}\.acc[_-][a-z0-9-]{10,32}\.[a-z0-9+/_=-]{30,120})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "sc_","ext_","scauth_","authress_",
]

[[rules]]
id = "aws-access-token"
description = "Identified a pattern that may indicate AWS credentials, risking unauthorized cloud resource access and data breaches on AWS platforms."
regex = '''(?:A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16}'''
keywords = [
    "akia","agpa","aida","aroa","aipa","anpa","anva","asia",
]

[[rules]]
id = "beamer-api-token"
description = "Detected a Beamer API token, potentially compromising content management and exposing sensitive notifications and updates."
regex = '''(?i)(?:beamer)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(b_[a-z0-9=_\-]{44})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "beamer",
]

[[rules]]
id = "bitbucket-client-id"
description = "Discovered a potential Bitbucket Client ID, risking unauthorized repository access and potential codebase exposure."
regex = '''(?i)(?:bitbucket)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "bitbucket",
]

[[rules]]
id = "bitbucket-client-secret"
description = "Discovered a potential Bitbucket Client Secret, posing a risk of compromised code repositories and unauthorized access."
regex = '''(?i)(?:bitbucket)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9=_\-]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "bitbucket",
]

[[rules]]
id = "bittrex-access-key"
description = "Identified a Bittrex Access Key, which could lead to unauthorized access to cryptocurrency trading accounts and financial loss."
regex = '''(?i)(?:bittrex)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "bittrex",
]

[[rules]]
id = "bittrex-secret-key"
description = "Detected a Bittrex Secret Key, potentially compromising cryptocurrency transactions and financial security."
regex = '''(?i)(?:bittrex)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "bittrex",
]

[[rules]]
id = "clojars-api-token"
description = "Uncovered a possible Clojars API token, risking unauthorized access to Clojure libraries and potential code manipulation."
regex = '''(?i)(CLOJARS_)[a-z0-9]{60}'''
keywords = [
    "clojars",
]

[[rules]]
id = "codecov-access-token"
description = "Found a pattern resembling a Codecov Access Token, posing a risk of unauthorized access to code coverage reports and sensitive data."
regex = '''(?i)(?:codecov)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "codecov",
]

[[rules]]
id = "coinbase-access-token"
description = "Detected a Coinbase Access Token, posing a risk of unauthorized access to cryptocurrency accounts and financial transactions."
regex = '''(?i)(?:coinbase)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9_-]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "coinbase",
]

[[rules]]
id = "confluent-access-token"
description = "Identified a Confluent Access Token, which could compromise access to streaming data platforms and sensitive data flow."
regex = '''(?i)(?:confluent)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{16})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "confluent",
]

[[rules]]
id = "confluent-secret-key"
description = "Found a Confluent Secret Key, potentially risking unauthorized operations and data access within Confluent services."
regex = '''(?i)(?:confluent)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "confluent",
]

[[rules]]
id = "contentful-delivery-api-token"
description = "Discovered a Contentful delivery API token, posing a risk to content management systems and data integrity."
regex = '''(?i)(?:contentful)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9=_\-]{43})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "contentful",
]

[[rules]]
id = "databricks-api-token"
description = "Uncovered a Databricks API token, which may compromise big data analytics platforms and sensitive data processing."
regex = '''(?i)\b(dapi[a-h0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "dapi",
]

[[rules]]
id = "datadog-access-token"
description = "Detected a Datadog Access Token, potentially risking monitoring and analytics data exposure and manipulation."
regex = '''(?i)(?:datadog)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{40})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "datadog",
]

[[rules]]
id = "defined-networking-api-token"
description = "Identified a Defined Networking API token, which could lead to unauthorized network operations and data breaches."
regex = '''(?i)(?:dnkey)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(dnkey-[a-z0-9=_\-]{26}-[a-z0-9=_\-]{52})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "dnkey",
]

[[rules]]
id = "digitalocean-access-token"
description = "Found a DigitalOcean OAuth Access Token, risking unauthorized cloud resource access and data compromise."
regex = '''(?i)\b(doo_v1_[a-f0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "doo_v1_",
]

[[rules]]
id = "digitalocean-pat"
description = "Discovered a DigitalOcean Personal Access Token, posing a threat to cloud infrastructure security and data privacy."
regex = '''(?i)\b(dop_v1_[a-f0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "dop_v1_",
]

[[rules]]
id = "digitalocean-refresh-token"
description = "Uncovered a DigitalOcean OAuth Refresh Token, which could allow prolonged unauthorized access and resource manipulation."
regex = '''(?i)\b(dor_v1_[a-f0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "dor_v1_",
]

[[rules]]
id = "discord-api-token"
description = "Detected a Discord API key, potentially compromising communication channels and user data privacy on Discord."
regex = '''(?i)(?:discord)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "discord",
]

[[rules]]
id = "discord-client-id"
description = "Identified a Discord client ID, which may lead to unauthorized integrations and data exposure in Discord applications."
regex = '''(?i)(?:discord)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9]{18})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "discord",
]

[[rules]]
id = "discord-client-secret"
description = "Discovered a potential Discord client secret, risking compromised Discord bot integrations and data leaks."
regex = '''(?i)(?:discord)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9=_\-]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "discord",
]

[[rules]]
id = "doppler-api-token"
description = "Discovered a Doppler API token, posing a risk to environment and secrets management security."
regex = '''(dp\.pt\.)(?i)[a-z0-9]{43}'''
keywords = [
    "doppler",
]

[[rules]]
id = "droneci-access-token"
description = "Detected a Droneci Access Token, potentially compromising continuous integration and deployment workflows."
regex = '''(?i)(?:droneci)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "droneci",
]

[[rules]]
id = "dropbox-api-token"
description = "Identified a Dropbox API secret, which could lead to unauthorized file access and data breaches in Dropbox storage."
regex = '''(?i)(?:dropbox)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{15})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "dropbox",
]

[[rules]]
id = "dropbox-long-lived-api-token"
description = "Found a Dropbox long-lived API token, risking prolonged unauthorized access to cloud storage and sensitive data."
regex = '''(?i)(?:dropbox)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{11}(AAAAAAAAAA)[a-z0-9\-_=]{43})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "dropbox",
]

[[rules]]
id = "dropbox-short-lived-api-token"
description = "Discovered a Dropbox short-lived API token, posing a risk of temporary but potentially harmful data access and manipulation."
regex = '''(?i)(?:dropbox)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(sl\.[a-z0-9\-=_]{135})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "dropbox",
]

[[rules]]
id = "duffel-api-token"
description = "Uncovered a Duffel API token, which may compromise travel platform integrations and sensitive customer data."
regex = '''duffel_(test|live)_(?i)[a-z0-9_\-=]{43}'''
keywords = [
    "duffel",
]

[[rules]]
id = "dynatrace-api-token"
description = "Detected a Dynatrace API token, potentially risking application performance monitoring and data exposure."
regex = '''dt0c01\.(?i)[a-z0-9]{24}\.[a-z0-9]{64}'''
keywords = [
    "dynatrace",
]

[[rules]]
id = "easypost-api-token"
description = "Identified an EasyPost API token, which could lead to unauthorized postal and shipment service access and data exposure."
regex = '''\bEZAK(?i)[a-z0-9]{54}'''
keywords = [
    "ezak",
]

[[rules]]
id = "easypost-test-api-token"
description = "Detected an EasyPost test API token, risking exposure of test environments and potentially sensitive shipment data."
regex = '''\bEZTK(?i)[a-z0-9]{54}'''
keywords = [
    "eztk",
]

[[rules]]
id = "etsy-access-token"
description = "Found an Etsy Access Token, potentially compromising Etsy shop management and customer data."
regex = '''(?i)(?:etsy)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{24})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "etsy",
]

[[rules]]
id = "facebook"
description = "Discovered a Facebook Access Token, posing a risk of unauthorized access to Facebook accounts and personal data exposure."
regex = '''(?i)(?:facebook)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "facebook",
]

[[rules]]
id = "fastly-api-token"
description = "Uncovered a Fastly API key, which may compromise CDN and edge cloud services, leading to content delivery and security issues."
regex = '''(?i)(?:fastly)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9=_\-]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "fastly",
]

[[rules]]
id = "finicity-api-token"
description = "Detected a Finicity API token, potentially risking financial data access and unauthorized financial operations."
regex = '''(?i)(?:finicity)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "finicity",
]

[[rules]]
id = "finicity-client-secret"
description = "Identified a Finicity Client Secret, which could lead to compromised financial service integrations and data breaches."
regex = '''(?i)(?:finicity)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{20})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "finicity",
]

[[rules]]
id = "finnhub-access-token"
description = "Found a Finnhub Access Token, risking unauthorized access to financial market data and analytics."
regex = '''(?i)(?:finnhub)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{20})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "finnhub",
]

[[rules]]
id = "flickr-access-token"
description = "Discovered a Flickr Access Token, posing a risk of unauthorized photo management and potential data leakage."
regex = '''(?i)(?:flickr)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "flickr",
]

[[rules]]
id = "flutterwave-encryption-key"
description = "Uncovered a Flutterwave Encryption Key, which may compromise payment processing and sensitive financial information."
regex = '''FLWSECK_TEST-(?i)[a-h0-9]{12}'''
keywords = [
    "flwseck_test",
]

[[rules]]
id = "flutterwave-public-key"
description = "Detected a Finicity Public Key, potentially exposing public cryptographic operations and integrations."
regex = '''FLWPUBK_TEST-(?i)[a-h0-9]{32}-X'''
keywords = [
    "flwpubk_test",
]

[[rules]]
id = "flutterwave-secret-key"
description = "Identified a Flutterwave Secret Key, risking unauthorized financial transactions and data breaches."
regex = '''FLWSECK_TEST-(?i)[a-h0-9]{32}-X'''
keywords = [
    "flwseck_test",
]

[[rules]]
id = "frameio-api-token"
description = "Found a Frame.io API token, potentially compromising video collaboration and project management."
regex = '''fio-u-(?i)[a-z0-9\-_=]{64}'''
keywords = [
    "fio-u-",
]

[[rules]]
id = "freshbooks-access-token"
description = "Discovered a Freshbooks Access Token, posing a risk to accounting software access and sensitive financial data exposure."
regex = '''(?i)(?:freshbooks)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "freshbooks",
]

[[rules]]
id = "gcp-api-key"
description = "Uncovered a GCP API key, which could lead to unauthorized access to Google Cloud services and data breaches."
regex = '''(?i)\b(AIza[0-9A-Za-z\\-_]{35})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "aiza",
]

[[rules]]
id = "generic-api-key"
description = "Detected a Generic API Key, potentially exposing access to various services and sensitive operations."
regex = '''(?i)(?:key|api|token|secret|client|passwd|password|auth|access|pass)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9a-z\-_.=]{10,150})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
entropy = 3.5
keywords = [
    "key","api","token","secret","client","passwd","password","auth","access",
]

[rules.allowlists]
stopwords = [
    "000000",
    "aaaaaa",
    "about",
    "abstract",
    "academy",
    "acces",
    "account",
    "act-",
    "act.",
    "act_",
    "action",
    "active",
    "actively",
    "activity",
    "adapter",
    "add-",
    "add.",
    "add_",
    "add-on",
    "addon",
    "addres",
    "admin",
    "adobe",
    "advanced",
    "adventure",
    "agent",
    "agile",
    "air-",
    "air.",
    "air_",
    "ajax",
    "akka",
    "alert",
    "alfred",
    "algorithm",
    "all-",
    "all.",
    "all_",
    "alloy",
    "alpha",
    "amazon",
    "amqp",
    "analysi",
    "analytic",
    "analyzer",
    "android",
    "angular",
    "angularj",
    "animate",
    "animation",
    "another",
    "ansible",
    "answer",
    "ant-",
    "ant.",
    "ant_",
    "any-",
    "any.",
    "any_",
    "apache",
    "app-",
    "app-",
    "app.",
    "app.",
    "app_",
    "app_",
    "apple",
    "arch",
    "archive",
    "archived",
    "arduino",
    "array",
    "art-",
    "art.",
    "art_",
    "article",
    "asp-",
    "asp.",
    "asp_",
    "asset",
    "async",
    "atom",
    "attention",
    "audio",
    "audit",
    "aura",
    "auth",
    "author",
    "author",
    "authorize",
    "auto",
    "automated",
    "automatic",
    "awesome",
    "aws_",
    "azure",
    "back",
    "backbone",
    "backend",
    "backup",
    "bar-",
    "bar.",
    "bar_",
    "base",
    "based",
    "bash",
    "basic",
    "batch",
    "been",
    "beer",
    "behavior",
    "being",
    "benchmark",
    "best",
    "beta",
    "better",
    "big-",
    "big.",
    "big_",
    "binary",
    "binding",
    "bit-",
    "bit.",
    "bit_",
    "bitcoin",
    "block",
    "blog",
    "board",
    "book",
    "bookmark",
    "boost",
    "boot",
    "bootstrap",
    "bosh",
    "bot-",
    "bot.",
    "bot_",
    "bower",
    "box-",
    "box.",
    "box_",
    "boxen",
    "bracket",
    "branch",
    "bridge",
    "browser",
    "brunch",
    "buffer",
    "bug-",
    "bug.",
    "bug_",
    "build",
    "builder",
    "building",
    "buildout",
    "buildpack",
    "built",
    "bundle",
    "busines",
    "but-",
    "but.",
    "but_",
    "button",
    "cache",
    "caching",
    "cakephp",
    "calendar",
    "call",
    "camera",
    "campfire",
    "can-",
    "can.",
    "can_",
    "canva",
    "captcha",
    "capture",
    "card",
    "carousel",
    "case",
    "cassandra",
    "cat-",
    "cat.",
    "cat_",
    "category",
    "center",
    "cento",
    "challenge",
    "change",
    "changelog",
    "channel",
    "chart",
    "chat",
    "cheat",
    "check",
    "checker",
    "chef",
    "ches",
    "chinese",
    "chosen",
    "chrome",
    "ckeditor",
    "clas",
    "classe",
    "classic",
    "clean",
    "cli-",
    "cli.",
    "cli_",
    "client",
    "client",
    "clojure",
    "clone",
    "closure",
    "cloud",
    "club",
    "cluster",
    "cms-",
    "cms_",
    "coco",
    "code",
    "coding",
    "coffee",
    "color",
    "combination",
    "combo",
    "command",
    "commander",
    "comment",
    "commit",
    "common",
    "community",
    "compas",
    "compiler",
    "complete",
    "component",
    "composer",
    "computer",
    "computing",
    "con-",
    "con.",
    "con_",
    "concept",
    "conf",
    "config",
    "config",
    "connect",
    "connector",
    "console",
    "contact",
    "container",
    "contao",
    "content",
    "contest",
    "context",
    "control",
    "convert",
    "converter",
    "conway'",
    "cookbook",
    "cookie",
    "cool",
    "copy",
    "cordova",
    "core",
    "couchbase",
    "couchdb",
    "countdown",
    "counter",
    "course",
    "craft",
    "crawler",
    "create",
    "creating",
    "creator",
    "credential",
    "crm-",
    "crm.",
    "crm_",
    "cros",
    "crud",
    "csv-",
    "csv.",
    "csv_",
    "cube",
    "cucumber",
    "cuda",
    "current",
    "currently",
    "custom",
    "daemon",
    "dark",
    "dart",
    "dash",
    "dashboard",
    "data",
    "database",
    "date",
    "day-",
    "day.",
    "day_",
    "dead",
    "debian",
    "debug",
    "debug",
    "debugger",
    "deck",
    "define",
    "del-",
    "del.",
    "del_",
    "delete",
    "demo",
    "deploy",
    "design",
    "designer",
    "desktop",
    "detection",
    "detector",
    "dev-",
    "dev.",
    "dev_",
    "develop",
    "developer",
    "device",
    "devise",
    "diff",
    "digital",
    "directive",
    "directory",
    "discovery",
    "display",
    "django",
    "dns-",
    "dns_",
    "doc-",
    "doc-",
    "doc.",
    "doc.",
    "doc_",
    "doc_",
    "docker",
    "docpad",
    "doctrine",
    "document",
    "doe-",
    "doe.",
    "doe_",
    "dojo",
    "dom-",
    "dom.",
    "dom_",
    "domain",
    "done",
    "don't",
    "dot-",
    "dot.",
    "dot_",
    "dotfile",
    "download",
    "draft",
    "drag",
    "drill",
    "drive",
    "driven",
    "driver",
    "drop",
    "dropbox",
    "drupal",
    "dsl-",
    "dsl.",
    "dsl_",
    "dynamic",
    "easy",
    "_ec2_",
    "ecdsa",
    "eclipse",
    "edit",
    "editing",
    "edition",
    "editor",
    "element",
    "emac",
    "email",
    "embed",
    "embedded",
    "ember",
    "emitter",
    "emulator",
    "encoding",
    "endpoint",
    "engine",
    "english",
    "enhanced",
    "entity",
    "entry",
    "env_",
    "episode",
    "erlang",
    "error",
    "espresso",
    "event",
    "evented",
    "example",
    "example",
    "exchange",
    "exercise",
    "experiment",
    "expire",
    "exploit",
    "explorer",
    "export",
    "exporter",
    "expres",
    "ext-",
    "ext.",
    "ext_",
    "extended",
    "extension",
    "external",
    "extra",
    "extractor",
    "fabric",
    "facebook",
    "factory",
    "fake",
    "fast",
    "feature",
    "feed",
    "fewfwef",
    "ffmpeg",
    "field",
    "file",
    "filter",
    "find",
    "finder",
    "firefox",
    "firmware",
    "first",
    "fish",
    "fix-",
    "fix_",
    "flash",
    "flask",
    "flat",
    "flex",
    "flexible",
    "flickr",
    "flow",
    "fluent",
    "fluentd",
    "fluid",
    "folder",
    "font",
    "force",
    "foreman",
    "fork",
    "form",
    "format",
    "formatter",
    "forum",
    "foundry",
    "framework",
    "free",
    "friend",
    "friendly",
    "front-end",
    "frontend",
    "ftp-",
    "ftp.",
    "ftp_",
    "fuel",
    "full",
    "fun-",
    "fun.",
    "fun_",
    "func",
    "future",
    "gaia",
    "gallery",
    "game",
    "gateway",
    "gem-",
    "gem.",
    "gem_",
    "gen-",
    "gen.",
    "gen_",
    "general",
    "generator",
    "generic",
    "genetic",
    "get-",
    "get.",
    "get_",
    "getenv",
    "getting",
    "ghost",
    "gist",
    "git-",
    "git.",
    "git_",
    "github",
    "gitignore",
    "gitlab",
    "glas",
    "gmail",
    "gnome",
    "gnu-",
    "gnu.",
    "gnu_",
    "goal",
    "golang",
    "gollum",
    "good",
    "google",
    "gpu-",
    "gpu.",
    "gpu_",
    "gradle",
    "grail",
    "graph",
    "graphic",
    "great",
    "grid",
    "groovy",
    "group",
    "grunt",
    "guard",
    "gui-",
    "gui.",
    "gui_",
    "guide",
    "guideline",
    "gulp",
    "gwt-",
    "gwt.",
    "gwt_",
    "hack",
    "hackathon",
    "hacker",
    "hacking",
    "hadoop",
    "haml",
    "handler",
    "hardware",
    "has-",
    "has_",
    "hash",
    "haskell",
    "have",
    "haxe",
    "hello",
    "help",
    "helper",
    "here",
    "hero",
    "heroku",
    "high",
    "hipchat",
    "history",
    "home",
    "homebrew",
    "homepage",
    "hook",
    "host",
    "hosting",
    "hot-",
    "hot.",
    "hot_",
    "house",
    "how-",
    "how.",
    "how_",
    "html",
    "http",
    "hub-",
    "hub.",
    "hub_",
    "hubot",
    "human",
    "icon",
    "ide-",
    "ide.",
    "ide_",
    "idea",
    "identity",
    "idiomatic",
    "image",
    "impact",
    "import",
    "important",
    "importer",
    "impres",
    "index",
    "infinite",
    "info",
    "injection",
    "inline",
    "input",
    "inside",
    "inspector",
    "instagram",
    "install",
    "installer",
    "instant",
    "intellij",
    "interface",
    "internet",
    "interview",
    "into",
    "intro",
    "ionic",
    "iphone",
    "ipython",
    "irc-",
    "irc_",
    "iso-",
    "iso.",
    "iso_",
    "issue",
    "jade",
    "jasmine",
    "java",
    "jbos",
    "jekyll",
    "jenkin",
    "job-",
    "job.",
    "job_",
    "joomla",
    "jpa-",
    "jpa.",
    "jpa_",
    "jquery",
    "json",
    "just",
    "kafka",
    "karma",
    "kata",
    "kernel",
    "keyboard",
    "kindle",
    "kit-",
    "kit.",
    "kit_",
    "kitchen",
    "knife",
    "koan",
    "kohana",
    "lab-",
    "lab-",
    "lab.",
    "lab.",
    "lab_",
    "lab_",
    "lambda",
    "lamp",
    "language",
    "laravel",
    "last",
    "latest",
    "latex",
    "launcher",
    "layer",
    "layout",
    "lazy",
    "ldap",
    "leaflet",
    "league",
    "learn",
    "learning",
    "led-",
    "led.",
    "led_",
    "leetcode",
    "les-",
    "les.",
    "les_",
    "level",
    "leveldb",
    "lib-",
    "lib.",
    "lib_",
    "librarie",
    "library",
    "license",
    "life",
    "liferay",
    "light",
    "lightbox",
    "like",
    "line",
    "link",
    "linked",
    "linkedin",
    "linux",
    "lisp",
    "list",
    "lite",
    "little",
    "load",
    "loader",
    "local",
    "location",
    "lock",
    "log-",
    "log.",
    "log_",
    "logger",
    "logging",
    "logic",
    "login",
    "logstash",
    "longer",
    "look",
    "love",
    "lua-",
    "lua.",
    "lua_",
    "mac-",
    "mac.",
    "mac_",
    "machine",
    "made",
    "magento",
    "magic",
    "mail",
    "make",
    "maker",
    "making",
    "man-",
    "man.",
    "man_",
    "manage",
    "manager",
    "manifest",
    "manual",
    "map-",
    "map-",
    "map.",
    "map.",
    "map_",
    "map_",
    "mapper",
    "mapping",
    "markdown",
    "markup",
    "master",
    "math",
    "matrix",
    "maven",
    "md5",
    "mean",
    "media",
    "mediawiki",
    "meetup",
    "memcached",
    "memory",
    "menu",
    "merchant",
    "message",
    "messaging",
    "meta",
    "metadata",
    "meteor",
    "method",
    "metric",
    "micro",
    "middleman",
    "migration",
    "minecraft",
    "miner",
    "mini",
    "minimal",
    "mirror",
    "mit-",
    "mit.",
    "mit_",
    "mobile",
    "mocha",
    "mock",
    "mod-",
    "mod.",
    "mod_",
    "mode",
    "model",
    "modern",
    "modular",
    "module",
    "modx",
    "money",
    "mongo",
    "mongodb",
    "mongoid",
    "mongoose",
    "monitor",
    "monkey",
    "more",
    "motion",
    "moved",
    "movie",
    "mozilla",
    "mqtt",
    "mule",
    "multi",
    "multiple",
    "music",
    "mustache",
    "mvc-",
    "mvc.",
    "mvc_",
    "mysql",
    "nagio",
    "name",
    "native",
    "need",
    "neo-",
    "neo.",
    "neo_",
    "nest",
    "nested",
    "net-",
    "net.",
    "net_",
    "nette",
    "network",
    "new-",
    "new-",
    "new.",
    "new.",
    "new_",
    "new_",
    "next",
    "nginx",
    "ninja",
    "nlp-",
    "nlp.",
    "nlp_",
    "node",
    "nodej",
    "nosql",
    "not-",
    "not.",
    "not_",
    "note",
    "notebook",
    "notepad",
    "notice",
    "notifier",
    "now-",
    "now.",
    "now_",
    "number",
    "oauth",
    "object",
    "objective",
    "obsolete",
    "ocaml",
    "octopres",
    "official",
    "old-",
    "old.",
    "old_",
    "onboard",
    "online",
    "only",
    "open",
    "opencv",
    "opengl",
    "openshift",
    "openwrt",
    "option",
    "oracle",
    "org-",
    "org.",
    "org_",
    "origin",
    "original",
    "orm-",
    "orm.",
    "orm_",
    "osx-",
    "osx_",
    "our-",
    "our.",
    "our_",
    "out-",
    "out.",
    "out_",
    "output",
    "over",
    "overview",
    "own-",
    "own.",
    "own_",
    "pack",
    "package",
    "packet",
    "page",
    "page",
    "panel",
    "paper",
    "paperclip",
    "para",
    "parallax",
    "parallel",
    "parse",
    "parser",
    "parsing",
    "particle",
    "party",
    "password",
    "patch",
    "path",
    "pattern",
    "payment",
    "paypal",
    "pdf-",
    "pdf.",
    "pdf_",
    "pebble",
    "people",
    "perl",
    "personal",
    "phalcon",
    "phoenix",
    "phone",
    "phonegap",
    "photo",
    "php-",
    "php.",
    "php_",
    "physic",
    "picker",
    "pipeline",
    "platform",
    "play",
    "player",
    "please",
    "plu-",
    "plu.",
    "plu_",
    "plug-in",
    "plugin",
    "plupload",
    "png-",
    "png.",
    "png_",
    "poker",
    "polyfill",
    "polymer",
    "pool",
    "pop-",
    "pop.",
    "pop_",
    "popcorn",
    "popup",
    "port",
    "portable",
    "portal",
    "portfolio",
    "post",
    "power",
    "powered",
    "powerful",
    "prelude",
    "pretty",
    "preview",
    "principle",
    "print",
    "pro-",
    "pro.",
    "pro_",
    "problem",
    "proc",
    "product",
    "profile",
    "profiler",
    "program",
    "progres",
    "project",
    "protocol",
    "prototype",
    "provider",
    "proxy",
    "public",
    "pull",
    "puppet",
    "pure",
    "purpose",
    "push",
    "pusher",
    "pyramid",
    "python",
    "quality",
    "query",
    "queue",
    "quick",
    "rabbitmq",
    "rack",
    "radio",
    "rail",
    "railscast",
    "random",
    "range",
    "raspberry",
    "rdf-",
    "rdf.",
    "rdf_",
    "react",
    "reactive",
    "read",
    "reader",
    "readme",
    "ready",
    "real",
    "reality",
    "real-time",
    "realtime",
    "recipe",
    "recorder",
    "red-",
    "red.",
    "red_",
    "reddit",
    "redi",
    "redmine",
    "reference",
    "refinery",
    "refresh",
    "registry",
    "related",
    "release",
    "remote",
    "rendering",
    "repo",
    "report",
    "request",
    "require",
    "required",
    "requirej",
    "research",
    "resource",
    "response",
    "resque",
    "rest",
    "restful",
    "resume",
    "reveal",
    "reverse",
    "review",
    "riak",
    "rich",
    "right",
    "ring",
    "robot",
    "role",
    "room",
    "router",
    "routing",
    "rpc-",
    "rpc.",
    "rpc_",
    "rpg-",
    "rpg.",
    "rpg_",
    "rspec",
    "ruby-",
    "ruby.",
    "ruby_",
    "rule",
    "run-",
    "run.",
    "run_",
    "runner",
    "running",
    "runtime",
    "rust",
    "rvm-",
    "rvm.",
    "rvm_",
    "salt",
    "sample",
    "sample",
    "sandbox",
    "sas-",
    "sas.",
    "sas_",
    "sbt-",
    "sbt.",
    "sbt_",
    "scala",
    "scalable",
    "scanner",
    "schema",
    "scheme",
    "school",
    "science",
    "scraper",
    "scratch",
    "screen",
    "script",
    "scroll",
    "scs-",
    "scs.",
    "scs_",
    "sdk-",
    "sdk.",
    "sdk_",
    "sdl-",
    "sdl.",
    "sdl_",
    "search",
    "secure",
    "security",
    "see-",
    "see.",
    "see_",
    "seed",
    "select",
    "selector",
    "selenium",
    "semantic",
    "sencha",
    "send",
    "sentiment",
    "serie",
    "server",
    "service",
    "session",
    "set-",
    "set.",
    "set_",
    "setting",
    "setting",
    "setup",
    "sha1",
    "sha2",
    "sha256",
    "share",
    "shared",
    "sharing",
    "sheet",
    "shell",
    "shield",
    "shipping",
    "shop",
    "shopify",
    "shortener",
    "should",
    "show",
    "showcase",
    "side",
    "silex",
    "simple",
    "simulator",
    "single",
    "site",
    "skeleton",
    "sketch",
    "skin",
    "slack",
    "slide",
    "slider",
    "slim",
    "small",
    "smart",
    "smtp",
    "snake",
    "snippet",
    "soap",
    "social",
    "socket",
    "software",
    "solarized",
    "solr",
    "solution",
    "solver",
    "some",
    "soon",
    "source",
    "space",
    "spark",
    "spatial",
    "spec",
    "sphinx",
    "spine",
    "spotify",
    "spree",
    "spring",
    "sprite",
    "sql-",
    "sql.",
    "sql_",
    "sqlite",
    "ssh-",
    "ssh.",
    "ssh_",
    "stack",
    "staging",
    "standard",
    "stanford",
    "start",
    "started",
    "starter",
    "startup",
    "stat",
    "statamic",
    "state",
    "static",
    "statistic",
    "statsd",
    "statu",
    "steam",
    "step",
    "still",
    "stm-",
    "stm.",
    "stm_",
    "storage",
    "store",
    "storm",
    "story",
    "strategy",
    "stream",
    "streaming",
    "string",
    "stripe",
    "structure",
    "studio",
    "study",
    "stuff",
    "style",
    "sublime",
    "sugar",
    "suite",
    "summary",
    "super",
    "support",
    "supported",
    "svg-",
    "svg.",
    "svg_",
    "svn-",
    "svn.",
    "svn_",
    "swagger",
    "swift",
    "switch",
    "switcher",
    "symfony",
    "symphony",
    "sync",
    "synopsi",
    "syntax",
    "system",
    "system",
    "tab-",
    "tab-",
    "tab.",
    "tab.",
    "tab_",
    "tab_",
    "table",
    "tag-",
    "tag-",
    "tag.",
    "tag.",
    "tag_",
    "tag_",
    "talk",
    "target",
    "task",
    "tcp-",
    "tcp.",
    "tcp_",
    "tdd-",
    "tdd.",
    "tdd_",
    "team",
    "tech",
    "template",
    "term",
    "terminal",
    "testing",
    "tetri",
    "text",
    "textmate",
    "theme",
    "theory",
    "three",
    "thrift",
    "time",
    "timeline",
    "timer",
    "tiny",
    "tinymce",
    "tip-",
    "tip.",
    "tip_",
    "title",
    "todo",
    "todomvc",
    "token",
    "tool",
    "toolbox",
    "toolkit",
    "top-",
    "top.",
    "top_",
    "tornado",
    "touch",
    "tower",
    "tracker",
    "tracking",
    "traffic",
    "training",
    "transfer",
    "translate",
    "transport",
    "tree",
    "trello",
    "try-",
    "try.",
    "try_",
    "tumblr",
    "tut-",
    "tut.",
    "tut_",
    "tutorial",
    "tweet",
    "twig",
    "twitter",
    "type",
    "typo",
    "ubuntu",
    "uiview",
    "ultimate",
    "under",
    "unit",
    "unity",
    "universal",
    "unix",
    "update",
    "updated",
    "upgrade",
    "upload",
    "uploader",
    "uri-",
    "uri.",
    "uri_",
    "url-",
    "url.",
    "url_",
    "usage",
    "usb-",
    "usb.",
    "usb_",
    "use-",
    "use.",
    "use_",
    "used",
    "useful",
    "user",
    "using",
    "util",
    "utilitie",
    "utility",
    "vagrant",
    "validator",
    "value",
    "variou",
    "varnish",
    "version",
    "via-",
    "via.",
    "via_",
    "video",
    "view",
    "viewer",
    "vim-",
    "vim.",
    "vim_",
    "vimrc",
    "virtual",
    "vision",
    "visual",
    "vpn",
    "want",
    "warning",
    "watch",
    "watcher",
    "wave",
    "way-",
    "way.",
    "way_",
    "weather",
    "web-",
    "web_",
    "webapp",
    "webgl",
    "webhook",
    "webkit",
    "webrtc",
    "website",
    "websocket",
    "welcome",
    "welcome",
    "what",
    "what'",
    "when",
    "where",
    "which",
    "why-",
    "why.",
    "why_",
    "widget",
    "wifi",
    "wiki",
    "win-",
    "win.",
    "win_",
    "window",
    "wip-",
    "wip.",
    "wip_",
    "within",
    "without",
    "wizard",
    "word",
    "wordpres",
    "work",
    "worker",
    "workflow",
    "working",
    "workshop",
    "world",
    "wrapper",
    "write",
    "writer",
    "writing",
    "written",
    "www-",
    "www.",
    "www_",
    "xamarin",
    "xcode",
    "xml-",
    "xml.",
    "xml_",
    "xmpp",
    "xxxxxx",
    "yahoo",
    "yaml",
    "yandex",
    "yeoman",
    "yet-",
    "yet.",
    "yet_",
    "yii-",
    "yii.",
    "yii_",
    "youtube",
    "yui-",
    "yui.",
    "yui_",
    "zend",
    "zero",
    "zip-",
    "zip.",
    "zip_",
    "zsh-",
    "zsh.",
    "zsh_",
]

[[rules]]
id = "github-app-token"
description = "Identified a GitHub App Token, which may compromise GitHub application integrations and source code security."
regex = '''(ghu|ghs)_[0-9a-zA-Z]{36}'''
keywords = [
    "ghu_","ghs_",
]

[[rules]]
id = "github-fine-grained-pat"
description = "Found a GitHub Fine-Grained Personal Access Token, risking unauthorized repository access and code manipulation."
regex = '''github_pat_[0-9a-zA-Z_]{82}'''
keywords = [
    "github_pat_",
]

[[rules]]
id = "github-oauth"
description = "Discovered a GitHub OAuth Access Token, posing a risk of compromised GitHub account integrations and data leaks."
regex = '''gho_[0-9a-zA-Z]{36}'''
keywords = [
    "gho_",
]

[[rules]]
id = "github-pat"
description = "Uncovered a GitHub Personal Access Token, potentially leading to unauthorized repository access and sensitive content exposure."
regex = '''ghp_[0-9a-zA-Z]{36}'''
keywords = [
    "ghp_",
]

[[rules]]
id = "github-refresh-token"
description = "Detected a GitHub Refresh Token, which could allow prolonged unauthorized access to GitHub services."
regex = '''ghr_[0-9a-zA-Z]{36}'''
keywords = [
    "ghr_",
]

[[rules]]
id = "gitlab-pat"
description = "Identified a GitLab Personal Access Token, risking unauthorized access to GitLab repositories and codebase exposure."
regex = '''glpat-[0-9a-zA-Z\-\_]{20}'''
keywords = [
    "glpat-",
]

[[rules]]
id = "gitlab-ptt"
description = "Found a GitLab Pipeline Trigger Token, potentially compromising continuous integration workflows and project security."
regex = '''glptt-[0-9a-f]{40}'''
keywords = [
    "glptt-",
]

[[rules]]
id = "gitlab-rrt"
description = "Discovered a GitLab Runner Registration Token, posing a risk to CI/CD pipeline integrity and unauthorized access."
regex = '''*********[0-9a-zA-Z\-\_]{20}'''
keywords = [
    "gr1348941",
]

[[rules]]
id = "gitter-access-token"
description = "Uncovered a Gitter Access Token, which may lead to unauthorized access to chat and communication services."
regex = '''(?i)(?:gitter)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9_-]{40})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "gitter",
]

[[rules]]
id = "gocardless-api-token"
description = "Detected a GoCardless API token, potentially risking unauthorized direct debit payment operations and financial data exposure."
regex = '''(?i)(?:gocardless)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(live_(?i)[a-z0-9\-_=]{40})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "live_","gocardless",
]

[[rules]]
id = "grafana-api-key"
description = "Identified a Grafana API key, which could compromise monitoring dashboards and sensitive data analytics."
regex = '''(?i)\b(eyJrIjoi[A-Za-z0-9]{70,400}={0,2})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "eyjrijoi",
]

[[rules]]
id = "grafana-cloud-api-token"
description = "Found a Grafana cloud API token, risking unauthorized access to cloud-based monitoring services and data exposure."
regex = '''(?i)\b(glc_[A-Za-z0-9+/]{32,400}={0,2})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "glc_",
]

[[rules]]
id = "grafana-service-account-token"
description = "Discovered a Grafana service account token, posing a risk of compromised monitoring services and data integrity."
regex = '''(?i)\b(glsa_[A-Za-z0-9]{32}_[A-Fa-f0-9]{8})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "glsa_",
]

[[rules]]
id = "hashicorp-tf-api-token"
description = "Uncovered a HashiCorp Terraform user/org API token, which may lead to unauthorized infrastructure management and security breaches."
regex = '''(?i)[a-z0-9]{14}\.atlasv1\.[a-z0-9\-_=]{60,70}'''
keywords = [
    "atlasv1",
]

[[rules]]
id = "hashicorp-tf-password"
description = "Identified a HashiCorp Terraform password field, risking unauthorized infrastructure configuration and security breaches."
regex = '''(?i)(?:administrator_login_password|password)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}("[a-z0-9=_\-]{8,20}")(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "administrator_login_password","password",
]

[[rules]]
id = "heroku-api-key"
description = "Detected a Heroku API Key, potentially compromising cloud application deployments and operational security."
regex = '''(?i)(?:heroku)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "heroku",
]

[[rules]]
id = "hubspot-api-key"
description = "Found a HubSpot API Token, posing a risk to CRM data integrity and unauthorized marketing operations."
regex = '''(?i)(?:hubspot)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "hubspot",
]

[[rules]]
id = "huggingface-access-token"
description = "Discovered a Hugging Face Access token, which could lead to unauthorized access to AI models and sensitive data."
regex = '''(?:^|[\\'"` >=:])(hf_[a-zA-Z]{34})(?:$|[\\'"` <])'''
entropy = 1
keywords = [
    "hf_",
]

[[rules]]
id = "huggingface-organization-api-token"
description = "Uncovered a Hugging Face Organization API token, potentially compromising AI organization accounts and associated data."
regex = '''(?:^|[\\'"` >=:\(,)])(api_org_[a-zA-Z]{34})(?:$|[\\'"` <\),])'''
entropy = 2
keywords = [
    "api_org_",
]

[[rules]]
id = "infracost-api-token"
description = "Detected an Infracost API Token, risking unauthorized access to cloud cost estimation tools and financial data."
regex = '''(?i)\b(ico-[a-zA-Z0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "ico-",
]

[[rules]]
id = "intercom-api-key"
description = "Identified an Intercom API Token, which could compromise customer communication channels and data privacy."
regex = '''(?i)(?:intercom)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9=_\-]{60})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "intercom",
]

[[rules]]
id = "jfrog-api-key"
description = "Found a JFrog API Key, posing a risk of unauthorized access to software artifact repositories and build pipelines."
regex = '''(?i)(?:jfrog|artifactory|bintray|xray)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{73})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "jfrog","artifactory","bintray","xray",
]

[[rules]]
id = "jfrog-identity-token"
description = "Discovered a JFrog Identity Token, potentially compromising access to JFrog services and sensitive software artifacts."
regex = '''(?i)(?:jfrog|artifactory|bintray|xray)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "jfrog","artifactory","bintray","xray",
]

[[rules]]
id = "jwt"
description = "Uncovered a JSON Web Token, which may lead to unauthorized access to web applications and sensitive user data."
regex = '''\b(ey[a-zA-Z0-9]{17,}\.ey[a-zA-Z0-9\/\\_-]{17,}\.(?:[a-zA-Z0-9\/\\_-]{10,}={0,2})?)(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "ey",
]

[[rules]]
id = "jwt-base64"
description = "Detected a Base64-encoded JSON Web Token, posing a risk of exposing encoded authentication and data exchange information."
regex = '''\bZXlK(?:(?P<alg>aGJHY2lPaU)|(?P<apu>aGNIVWlPaU)|(?P<apv>aGNIWWlPaU)|(?P<aud>aGRXUWlPaU)|(?P<b64>aU5qUWlP)|(?P<crit>amNtbDBJanBi)|(?P<cty>amRIa2lPaU)|(?P<epk>bGNHc2lPbn)|(?P<enc>bGJtTWlPaU)|(?P<jku>cWEzVWlPaU)|(?P<jwk>cWQyc2lPb)|(?P<iss>cGMzTWlPaU)|(?P<iv>cGRpSTZJ)|(?P<kid>cmFXUWlP)|(?P<key_ops>clpYbGZiM0J6SWpwY)|(?P<kty>cmRIa2lPaUp)|(?P<nonce>dWIyNWpaU0k2)|(?P<p2c>d01tTWlP)|(?P<p2s>d01uTWlPaU)|(?P<ppt>d2NIUWlPaU)|(?P<sub>emRXSWlPaU)|(?P<svt>emRuUWlP)|(?P<tag>MFlXY2lPaU)|(?P<typ>MGVYQWlPaUp)|(?P<url>MWNtd2l)|(?P<use>MWMyVWlPaUp)|(?P<ver>MlpYSWlPaU)|(?P<version>MlpYSnphVzl1SWpv)|(?P<x>NElqb2)|(?P<x5c>NE5XTWlP)|(?P<x5t>NE5YUWlPaU)|(?P<x5ts256>NE5YUWpVekkxTmlJNkl)|(?P<x5u>NE5YVWlPaU)|(?P<zip>NmFYQWlPaU))[a-zA-Z0-9\/\\_+\-\r\n]{40,}={0,2}'''
keywords = [
    "zxlk",
]

[[rules]]
id = "kraken-access-token"
description = "Identified a Kraken Access Token, potentially compromising cryptocurrency trading accounts and financial security."
regex = '''(?i)(?:kraken)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9\/=_\+\-]{80,90})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "kraken",
]

[[rules]]
id = "kucoin-access-token"
description = "Found a Kucoin Access Token, risking unauthorized access to cryptocurrency exchange services and transactions."
regex = '''(?i)(?:kucoin)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{24})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "kucoin",
]

[[rules]]
id = "kucoin-secret-key"
description = "Discovered a Kucoin Secret Key, which could lead to compromised cryptocurrency operations and financial data breaches."
regex = '''(?i)(?:kucoin)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "kucoin",
]

[[rules]]
id = "launchdarkly-access-token"
description = "Uncovered a Launchdarkly Access Token, potentially compromising feature flag management and application functionality."
regex = '''(?i)(?:launchdarkly)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9=_\-]{40})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "launchdarkly",
]

[[rules]]
id = "linear-api-key"
description = "Detected a Linear API Token, posing a risk to project management tools and sensitive task data."
regex = '''lin_api_(?i)[a-z0-9]{40}'''
keywords = [
    "lin_api_",
]

[[rules]]
id = "linear-client-secret"
description = "Identified a Linear Client Secret, which may compromise secure integrations and sensitive project management data."
regex = '''(?i)(?:linear)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "linear",
]

[[rules]]
id = "linkedin-client-id"
description = "Found a LinkedIn Client ID, risking unauthorized access to LinkedIn integrations and professional data exposure."
regex = '''(?i)(?:linkedin|linked-in)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{14})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "linkedin","linked-in",
]

[[rules]]
id = "linkedin-client-secret"
description = "Discovered a LinkedIn Client secret, potentially compromising LinkedIn application integrations and user data."
regex = '''(?i)(?:linkedin|linked-in)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{16})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "linkedin","linked-in",
]

[[rules]]
id = "lob-api-key"
description = "Uncovered a Lob API Key, which could lead to unauthorized access to mailing and address verification services."
regex = '''(?i)(?:lob)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}((live|test)_[a-f0-9]{35})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "test_","live_",
]

[[rules]]
id = "lob-pub-api-key"
description = "Detected a Lob Publishable API Key, posing a risk of exposing mail and print service integrations."
regex = '''(?i)(?:lob)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}((test|live)_pub_[a-f0-9]{31})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "test_pub","live_pub","_pub",
]

[[rules]]
id = "mailchimp-api-key"
description = "Identified a Mailchimp API key, potentially compromising email marketing campaigns and subscriber data."
regex = '''(?i)(?:mailchimp)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{32}-us20)(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "mailchimp",
]

[[rules]]
id = "mailgun-private-api-token"
description = "Found a Mailgun private API token, risking unauthorized email service operations and data breaches."
regex = '''(?i)(?:mailgun)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(key-[a-f0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "mailgun",
]

[[rules]]
id = "mailgun-pub-key"
description = "Discovered a Mailgun public validation key, which could expose email verification processes and associated data."
regex = '''(?i)(?:mailgun)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(pubkey-[a-f0-9]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "mailgun",
]

[[rules]]
id = "mailgun-signing-key"
description = "Uncovered a Mailgun webhook signing key, potentially compromising email automation and data integrity."
regex = '''(?i)(?:mailgun)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-h0-9]{32}-[a-h0-9]{8}-[a-h0-9]{8})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "mailgun",
]

[[rules]]
id = "mapbox-api-token"
description = "Detected a MapBox API token, posing a risk to geospatial services and sensitive location data exposure."
regex = '''(?i)(?:mapbox)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(pk\.[a-z0-9]{60}\.[a-z0-9]{22})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "mapbox",
]

[[rules]]
id = "mattermost-access-token"
description = "Identified a Mattermost Access Token, which may compromise team communication channels and data privacy."
regex = '''(?i)(?:mattermost)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{26})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "mattermost",
]

[[rules]]
id = "messagebird-api-token"
description = "Found a MessageBird API token, risking unauthorized access to communication platforms and message data."
regex = '''(?i)(?:messagebird|message-bird|message_bird)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{25})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "messagebird","message-bird","message_bird",
]

[[rules]]
id = "messagebird-client-id"
description = "Discovered a MessageBird client ID, potentially compromising API integrations and sensitive communication data."
regex = '''(?i)(?:messagebird|message-bird|message_bird)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "messagebird","message-bird","message_bird",
]

[[rules]]
id = "microsoft-teams-webhook"
description = "Uncovered a Microsoft Teams Webhook, which could lead to unauthorized access to team collaboration tools and data leaks."
regex = '''https:\/\/[a-z0-9]+\.webhook\.office\.com\/webhookb2\/[a-z0-9]{8}-([a-z0-9]{4}-){3}[a-z0-9]{12}@[a-z0-9]{8}-([a-z0-9]{4}-){3}[a-z0-9]{12}\/IncomingWebhook\/[a-z0-9]{32}\/[a-z0-9]{8}-([a-z0-9]{4}-){3}[a-z0-9]{12}'''
keywords = [
    "webhook.office.com","webhookb2","incomingwebhook",
]

[[rules]]
id = "netlify-access-token"
description = "Detected a Netlify Access Token, potentially compromising web hosting services and site management."
regex = '''(?i)(?:netlify)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9=_\-]{40,46})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "netlify",
]

[[rules]]
id = "new-relic-browser-api-token"
description = "Identified a New Relic ingest browser API token, risking unauthorized access to application performance data and analytics."
regex = '''(?i)(?:new-relic|newrelic|new_relic)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(NRJS-[a-f0-9]{19})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "nrjs-",
]

[[rules]]
id = "new-relic-user-api-id"
description = "Found a New Relic user API ID, posing a risk to application monitoring services and data integrity."
regex = '''(?i)(?:new-relic|newrelic|new_relic)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "new-relic","newrelic","new_relic",
]

[[rules]]
id = "new-relic-user-api-key"
description = "Discovered a New Relic user API Key, which could lead to compromised application insights and performance monitoring."
regex = '''(?i)(?:new-relic|newrelic|new_relic)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(NRAK-[a-z0-9]{27})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "nrak",
]

[[rules]]
id = "npm-access-token"
description = "Uncovered an npm access token, potentially compromising package management and code repository access."
regex = '''(?i)\b(npm_[a-z0-9]{36})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "npm_",
]

[[rules]]
id = "nytimes-access-token"
description = "Detected a Nytimes Access Token, risking unauthorized access to New York Times APIs and content services."
regex = '''(?i)(?:nytimes|new-york-times,|newyorktimes)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9=_\-]{32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "nytimes","new-york-times","newyorktimes",
]

[[rules]]
id = "okta-access-token"
description = "Identified an Okta Access Token, which may compromise identity management services and user authentication data."
regex = '''(?i)(?:okta)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9=_\-]{42})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "okta",
]

[[rules]]
id = "openai-api-key"
description = "Found an OpenAI API Key, posing a risk of unauthorized access to AI services and data manipulation."
regex = '''(?i)\b(sk-[a-zA-Z0-9]{20}T3BlbkFJ[a-zA-Z0-9]{20})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "t3blbkfj",
]

[[rules]]
id = "plaid-api-token"
description = "Discovered a Plaid API Token, potentially compromising financial data aggregation and banking services."
regex = '''(?i)(?:plaid)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(access-(?:sandbox|development|production)-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "plaid",
]

[[rules]]
id = "plaid-client-id"
description = "Uncovered a Plaid Client ID, which could lead to unauthorized financial service integrations and data breaches."
regex = '''(?i)(?:plaid)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{24})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
entropy = 3.5
keywords = [
    "plaid",
]

[[rules]]
id = "plaid-secret-key"
description = "Detected a Plaid Secret key, risking unauthorized access to financial accounts and sensitive transaction data."
regex = '''(?i)(?:plaid)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{30})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
entropy = 3.5
keywords = [
    "plaid",
]

[[rules]]
id = "planetscale-api-token"
description = "Identified a PlanetScale API token, potentially compromising database management and operations."
regex = '''(?i)\b(pscale_tkn_(?i)[a-z0-9=\-_\.]{32,64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "pscale_tkn_",
]

[[rules]]
id = "planetscale-oauth-token"
description = "Found a PlanetScale OAuth token, posing a risk to database access control and sensitive data integrity."
regex = '''(?i)\b(pscale_oauth_(?i)[a-z0-9=\-_\.]{32,64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "pscale_oauth_",
]

[[rules]]
id = "planetscale-password"
description = "Discovered a PlanetScale password, which could lead to unauthorized database operations and data breaches."
regex = '''(?i)\b(pscale_pw_(?i)[a-z0-9=\-_\.]{32,64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "pscale_pw_",
]

[[rules]]
id = "postman-api-token"
description = "Uncovered a Postman API token, potentially compromising API testing and development workflows."
regex = '''(?i)\b(PMAK-(?i)[a-f0-9]{24}\-[a-f0-9]{34})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "pmak-",
]

[[rules]]
id = "prefect-api-token"
description = "Detected a Prefect API token, risking unauthorized access to workflow management and automation services."
regex = '''(?i)\b(pnu_[a-z0-9]{36})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "pnu_",
]

[[rules]]
id = "private-key"
description = "Identified a Private Key, which may compromise cryptographic security and sensitive data encryption."
regex = '''(?i)-----BEGIN[ A-Z0-9_-]{0,100}PRIVATE KEY( BLOCK)?-----[\s\S-]*KEY( BLOCK)?----'''
keywords = [
    "-----begin",
]

[[rules]]
id = "pulumi-api-token"
description = "Found a Pulumi API token, posing a risk to infrastructure as code services and cloud resource management."
regex = '''(?i)\b(pul-[a-f0-9]{40})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "pul-",
]

[[rules]]
id = "pypi-upload-token"
description = "Discovered a PyPI upload token, potentially compromising Python package distribution and repository integrity."
regex = '''pypi-AgEIcHlwaS5vcmc[A-Za-z0-9\-_]{50,1000}'''
keywords = [
    "pypi-ageichlwas5vcmc",
]

[[rules]]
id = "rapidapi-access-token"
description = "Uncovered a RapidAPI Access Token, which could lead to unauthorized access to various APIs and data services."
regex = '''(?i)(?:rapidapi)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9_-]{50})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "rapidapi",
]

[[rules]]
id = "readme-api-token"
description = "Detected a Readme API token, risking unauthorized documentation management and content exposure."
regex = '''(?i)\b(rdme_[a-z0-9]{70})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "rdme_",
]

[[rules]]
id = "rubygems-api-token"
description = "Identified a Rubygem API token, potentially compromising Ruby library distribution and package management."
regex = '''(?i)\b(rubygems_[a-f0-9]{48})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "rubygems_",
]

[[rules]]
id = "scalingo-api-token"
description = "Found a Scalingo API token, posing a risk to cloud platform services and application deployment security."
regex = '''\btk-us-[a-zA-Z0-9-_]{48}\b'''
keywords = [
    "tk-us-",
]

[[rules]]
id = "sendbird-access-id"
description = "Discovered a Sendbird Access ID, which could compromise chat and messaging platform integrations."
regex = '''(?i)(?:sendbird)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "sendbird",
]

[[rules]]
id = "sendbird-access-token"
description = "Uncovered a Sendbird Access Token, potentially risking unauthorized access to communication services and user data."
regex = '''(?i)(?:sendbird)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{40})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "sendbird",
]

[[rules]]
id = "sendgrid-api-token"
description = "Detected a SendGrid API token, posing a risk of unauthorized email service operations and data exposure."
regex = '''(?i)\b(SG\.(?i)[a-z0-9=_\-\.]{66})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "sg.",
]

[[rules]]
id = "sendinblue-api-token"
description = "Identified a Sendinblue API token, which may compromise email marketing services and subscriber data privacy."
regex = '''(?i)\b(xkeysib-[a-f0-9]{64}\-(?i)[a-z0-9]{16})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "xkeysib-",
]

[[rules]]
id = "sentry-access-token"
description = "Found a Sentry Access Token, risking unauthorized access to error tracking services and sensitive application data."
regex = '''(?i)(?:sentry)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "sentry",
]

[[rules]]
id = "shippo-api-token"
description = "Discovered a Shippo API token, potentially compromising shipping services and customer order data."
regex = '''(?i)\b(shippo_(live|test)_[a-f0-9]{40})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "shippo_",
]

[[rules]]
id = "shopify-access-token"
description = "Uncovered a Shopify access token, which could lead to unauthorized e-commerce platform access and data breaches."
regex = '''shpat_[a-fA-F0-9]{32}'''
keywords = [
    "shpat_",
]

[[rules]]
id = "shopify-custom-access-token"
description = "Detected a Shopify custom access token, potentially compromising custom app integrations and e-commerce data security."
regex = '''shpca_[a-fA-F0-9]{32}'''
keywords = [
    "shpca_",
]

[[rules]]
id = "shopify-private-app-access-token"
description = "Identified a Shopify private app access token, risking unauthorized access to private app data and store operations."
regex = '''shppa_[a-fA-F0-9]{32}'''
keywords = [
    "shppa_",
]

[[rules]]
id = "shopify-shared-secret"
description = "Found a Shopify shared secret, posing a risk to application authentication and e-commerce platform security."
regex = '''shpss_[a-fA-F0-9]{32}'''
keywords = [
    "shpss_",
]

[[rules]]
id = "sidekiq-secret"
description = "Discovered a Sidekiq Secret, which could lead to compromised background job processing and application data breaches."
regex = '''(?i)(?:BUNDLE_ENTERPRISE__CONTRIBSYS__COM|BUNDLE_GEMS__CONTRIBSYS__COM)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-f0-9]{8}:[a-f0-9]{8})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "bundle_enterprise__contribsys__com","bundle_gems__contribsys__com",
]

[[rules]]
id = "sidekiq-sensitive-url"
description = "Uncovered a Sidekiq Sensitive URL, potentially exposing internal job queues and sensitive operation details."
regex = '''(?i)\b(http(?:s??):\/\/)([a-f0-9]{8}:[a-f0-9]{8})@(?:gems.contribsys.com|enterprise.contribsys.com)(?:[\/|\#|\?|:]|$)'''
secretGroup = 2
keywords = [
    "gems.contribsys.com","enterprise.contribsys.com",
]

[[rules]]
id = "slack-app-token"
description = "Detected a Slack App-level token, risking unauthorized access to Slack applications and workspace data."
regex = '''(?i)(xapp-\d-[A-Z0-9]+-\d+-[a-z0-9]+)'''
keywords = [
    "xapp",
]

[[rules]]
id = "slack-bot-token"
description = "Identified a Slack Bot token, which may compromise bot integrations and communication channel security."
regex = '''(xoxb-[0-9]{10,13}\-[0-9]{10,13}[a-zA-Z0-9-]*)'''
keywords = [
    "xoxb",
]

[[rules]]
id = "slack-config-access-token"
description = "Found a Slack Configuration access token, posing a risk to workspace configuration and sensitive data access."
regex = '''(?i)(xoxe.xox[bp]-\d-[A-Z0-9]{163,166})'''
keywords = [
    "xoxe.xoxb-","xoxe.xoxp-",
]

[[rules]]
id = "slack-config-refresh-token"
description = "Discovered a Slack Configuration refresh token, potentially allowing prolonged unauthorized access to configuration settings."
regex = '''(?i)(xoxe-\d-[A-Z0-9]{146})'''
keywords = [
    "xoxe-",
]

[[rules]]
id = "slack-legacy-bot-token"
description = "Uncovered a Slack Legacy bot token, which could lead to compromised legacy bot operations and data exposure."
regex = '''(xoxb-[0-9]{8,14}\-[a-zA-Z0-9]{18,26})'''
keywords = [
    "xoxb",
]

[[rules]]
id = "slack-legacy-token"
description = "Detected a Slack Legacy token, risking unauthorized access to older Slack integrations and user data."
regex = '''(xox[os]-\d+-\d+-\d+-[a-fA-F\d]+)'''
keywords = [
    "xoxo","xoxs",
]

[[rules]]
id = "slack-legacy-workspace-token"
description = "Identified a Slack Legacy Workspace token, potentially compromising access to workspace data and legacy features."
regex = '''(xox[ar]-(?:\d-)?[0-9a-zA-Z]{8,48})'''
keywords = [
    "xoxa","xoxr",
]

[[rules]]
id = "slack-user-token"
description = "Found a Slack User token, posing a risk of unauthorized user impersonation and data access within Slack workspaces."
regex = '''(xox[pe](?:-[0-9]{10,13}){3}-[a-zA-Z0-9-]{28,34})'''
keywords = [
    "xoxp-","xoxe-",
]

[[rules]]
id = "slack-webhook-url"
description = "Discovered a Slack Webhook, which could lead to unauthorized message posting and data leakage in Slack channels."
regex = '''(https?:\/\/)?hooks.slack.com\/(services|workflows)\/[A-Za-z0-9+\/]{43,46}'''
keywords = [
    "hooks.slack.com",
]

[[rules]]
id = "snyk-api-token"
description = "Uncovered a Snyk API token, potentially compromising software vulnerability scanning and code security."
regex = '''(?i)(?:snyk_token|snyk_key|snyk_api_token|snyk_api_key|snyk_oauth_token)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "snyk_token","snyk_key","snyk_api_token","snyk_api_key","snyk_oauth_token",
]

[[rules]]
id = "square-access-token"
description = "Detected a Square Access Token, risking unauthorized payment processing and financial transaction exposure."
regex = '''(?i)\b(sq0atp-[0-9A-Za-z\-_]{22})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "sq0atp-",
]

[[rules]]
id = "squarespace-access-token"
description = "Identified a Squarespace Access Token, which may compromise website management and content control on Squarespace."
regex = '''(?i)(?:squarespace)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "squarespace",
]

[[rules]]
id = "stripe-access-token"
description = "Found a Stripe Access Token, posing a risk to payment processing services and sensitive financial data."
regex = '''(?i)\b((sk|pk)_(test|live)_[0-9a-z]{10,32})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "sk_test","pk_test","sk_live","pk_live",
]

[[rules]]
id = "sumologic-access-id"
description = "Discovered a SumoLogic Access ID, potentially compromising log management services and data analytics integrity."
regex = '''(?i:(?:sumo)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3})(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(su[a-zA-Z0-9]{12})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
entropy = 3
keywords = [
    "sumo",
]

[rules.allowlist]

regexTarget = "line"
regexes = [
    "sumOf",
]

[[rules]]
id = "sumologic-access-token"
description = "Uncovered a SumoLogic Access Token, which could lead to unauthorized access to log data and analytics insights."
regex = '''(?i)(?:sumo)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{64})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
entropy = 3
keywords = [
    "sumo",
]

[[rules]]
id = "telegram-bot-api-token"
description = "Detected a Telegram Bot API Token, risking unauthorized bot operations and message interception on Telegram."
regex = '''(?i)(?:^|[^0-9])([0-9]{5,16}:A[a-zA-Z0-9_\-]{34})(?:$|[^a-zA-Z0-9_\-])'''
keywords = [
    "telegram","api","bot","token","url",
]

[[rules]]
id = "travisci-access-token"
description = "Identified a Travis CI Access Token, potentially compromising continuous integration services and codebase security."
regex = '''(?i)(?:travis)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{22})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "travis",
]

[[rules]]
id = "twilio-api-key"
description = "Found a Twilio API Key, posing a risk to communication services and sensitive customer interaction data."
regex = '''SK[0-9a-fA-F]{32}'''
keywords = [
    "twilio",
]

[[rules]]
id = "twitch-api-token"
description = "Discovered a Twitch API token, which could compromise streaming services and account integrations."
regex = '''(?i)(?:twitch)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{30})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "twitch",
]

[[rules]]
id = "twitter-access-secret"
description = "Uncovered a Twitter Access Secret, potentially risking unauthorized Twitter integrations and data breaches."
regex = '''(?i)(?:twitter)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{45})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "twitter",
]

[[rules]]
id = "twitter-access-token"
description = "Detected a Twitter Access Token, posing a risk of unauthorized account operations and social media data exposure."
regex = '''(?i)(?:twitter)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([0-9]{15,25}-[a-zA-Z0-9]{20,40})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "twitter",
]

[[rules]]
id = "twitter-api-key"
description = "Identified a Twitter API Key, which may compromise Twitter application integrations and user data security."
regex = '''(?i)(?:twitter)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{25})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "twitter",
]

[[rules]]
id = "twitter-api-secret"
description = "Found a Twitter API Secret, risking the security of Twitter app integrations and sensitive data access."
regex = '''(?i)(?:twitter)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{50})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "twitter",
]

[[rules]]
id = "twitter-bearer-token"
description = "Discovered a Twitter Bearer Token, potentially compromising API access and data retrieval from Twitter."
regex = '''(?i)(?:twitter)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(A{22}[a-zA-Z0-9%]{80,100})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "twitter",
]

[[rules]]
id = "typeform-api-token"
description = "Uncovered a Typeform API token, which could lead to unauthorized survey management and data collection."
regex = '''(?i)(?:typeform)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(tfp_[a-z0-9\-_\.=]{59})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "tfp_",
]

[[rules]]
id = "vault-batch-token"
description = "Detected a Vault Batch Token, risking unauthorized access to secret management services and sensitive data."
regex = '''(?i)\b(hvb\.[a-z0-9_-]{138,212})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "hvb",
]

[[rules]]
id = "vault-service-token"
description = "Identified a Vault Service Token, potentially compromising infrastructure security and access to sensitive credentials."
regex = '''(?i)\b(hvs\.[a-z0-9_-]{90,100})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "hvs",
]

[[rules]]
id = "yandex-access-token"
description = "Found a Yandex Access Token, posing a risk to Yandex service integrations and user data privacy."
regex = '''(?i)(?:yandex)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(t1\.[A-Z0-9a-z_-]+[=]{0,2}\.[A-Z0-9a-z_-]{86}[=]{0,2})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "yandex",
]

[[rules]]
id = "yandex-api-key"
description = "Discovered a Yandex API Key, which could lead to unauthorized access to Yandex services and data manipulation."
regex = '''(?i)(?:yandex)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(AQVN[A-Za-z0-9_\-]{35,38})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "yandex",
]

[[rules]]
id = "yandex-aws-access-token"
description = "Uncovered a Yandex AWS Access Token, potentially compromising cloud resource access and data security on Yandex Cloud."
regex = '''(?i)(?:yandex)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}(YC[a-zA-Z0-9_\-]{38})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "yandex",
]

[[rules]]
id = "zendesk-secret-key"
description = "Detected a Zendesk Secret Key, risking unauthorized access to customer support services and sensitive ticketing data."
regex = '''(?i)(?:zendesk)(?:[0-9a-z\-_\t .]{0,20})(?:[\s|']|[\s|"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:'|\"|\s|=|\x60){0,5}([a-z0-9]{40})(?:['|\"|\n|\r|\s|\x60|;]|$)'''
keywords = [
    "zendesk",
]
