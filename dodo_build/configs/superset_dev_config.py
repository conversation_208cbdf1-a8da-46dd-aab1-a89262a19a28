import logging

from sqlalchemy.pool import QueuePool

# Enables SWAGGER UI for superset openapi spec
# ex: http://localhost:8080/swagger/v1

logger = logging.getLogger(__name__)
logger.addHandler(logging.StreamHandler())

FAB_API_SWAGGER_UI = True

# CORS Options
ENABLE_CORS = True
CORS_OPTIONS = {
    "origins": [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "https://localhost:3000",
        "https://127.0.0.1:3000",
    ],
    "supports_credentials": True,
}

# Disable CSRF protection on dev
WTF_CSRF_ENABLED = False
SILENCE_FAB = False

SQLALCHEMY_ENGINE_OPTIONS = {
    "poolclass": QueuePool,
    "pool_size": 30,
    "max_overflow": 20,
    "pool_recycle": 120,
    "connect_args": {"connect_timeout": 5},
    "hide_parameters": True,
}

HTML_SANITIZATION = False

KAFKA_TOPIC = "superset.log.v1"
try:
    KAFKA_CONFIG = {
        "bootstrap.servers": "kafka.spr:9092",
    }
except Exception:
    logger.warning("Cannot take variables for kafka in DEV config")

SHOW_STACKTRACE = True
