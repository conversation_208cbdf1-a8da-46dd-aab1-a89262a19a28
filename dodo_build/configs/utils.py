import json
import logging
import os

from confluent_kafka import Producer
from confluent_kafka.error import KafkaError
from flask import current_app
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)
KAFKA_LIMIT = 5


def get_env_variable(var_name: str, default: str | None = None) -> str:
    """Get the environment variable or raise exception."""
    try:
        return os.environ[var_name]
    except KeyError:
        if default is not None:
            return default
        else:
            error_msg = f"The environment variable {var_name} was missing, abort..."
            raise EnvironmentError(error_msg)


def is_development() -> bool:
    return get_env_variable("SUPERSET_ENV", "development") == "development"


def send2kafka(session: Session, num_of_records: int) -> None:
    """
    Send log records to Kafka in batches.

    Args:
        session (Session): SQLAlchemy session.
        num_of_records (int): Number of records to fetch and send.
    """
    try:
        from flask_appbuilder.security.sqla.models import User

        from superset.models.core import Log
        from superset.models.dashboard import Dashboard
        from superset.models.slice import Slice
        from superset.models.team import Team, team_users
        from superset.models.user_info import UserInfo

        messages = (
            session.query(
                Log.id,
                User.first_name,
                User.last_name,
                User.email,
                Dashboard.dashboard_title,
                Dashboard.dashboard_title_ru,
                Slice.slice_name,
                Slice.slice_name_ru,
                Log.json,
                Log.dttm,
                Log.duration_ms,
                Log.referrer,
                Log.dashboard_id,
                Log.slice_id,
                Team.slug,
                Team.name,
                Log.is_plugin,
                UserInfo.data_auth_dodo,
                UserInfo.language,
                UserInfo.country_name,
                Log.action,
            )
            .join(User, User.id == Log.user_id, isouter=True)
            .join(Slice, Slice.id == Log.slice_id, isouter=True)
            .join(Dashboard, Dashboard.id == Log.dashboard_id, isouter=True)
            .join(UserInfo, User.id == UserInfo.user_id, isouter=True)
            .join(team_users, Log.user_id == team_users.c.user_id, isouter=True)
            .join(Team, Team.id == team_users.c.team_id, isouter=True)
            .order_by(Log.id.desc())
            .limit(num_of_records)
            .all()
        )

        producer = Producer(current_app.config["KAFKA_CONFIG"])
        result_list = []
        for message in messages:
            d_message = {
                "log_id": message[0],
                "first_name": message[1],
                "last_name": message[2],
                "email": message[3],
                "dashboard_title": message[4],
                "dashboard_title_RU": message[5],
                "slice_name": message[6],
                "slice_name_RU": message[7],
                "json": message[8],
                "dttm": message[9].isoformat(),
                "duration_ms": message[10],
                "referrer": message[11],
                "dashboard_id": message[12],
                "slice_id": message[13],
                "slug": message[14],
                "name": message[15],
                "is_plugin": message[16],
                "data_auth_dodo": message[17],
                "language": message[18],
                "country_name": message[19],
                "action": message[20],
            }
            result_list.append(d_message)

            if len(result_list) == KAFKA_LIMIT:
                bytes_message = json.dumps(result_list).encode()
                producer.produce(current_app.config["KAFKA_TOPIC"], value=bytes_message)
                producer.flush()
                result_list = []

        if result_list:
            bytes_message = json.dumps(result_list).encode()
            producer.produce(current_app.config["KAFKA_TOPIC"], value=bytes_message)
            producer.flush()

    except KafkaError as e:
        logger.error("[kafka.py] KafkaError: %s", e)
    except Exception as e:
        logger.error("[kafka.py] Failed to send message to Kafka: %s", e)
