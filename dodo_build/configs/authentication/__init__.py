from flask_appbuilder.security.manager import AUTH_OAUTH
from utils import get_env_variable, is_development

from .dodo import (
    DODO_OAUTH_PROVIDER_AZURE,
    DODO_OAUTH_PROVIDER_YANDEX,
    DodoDevSecurityManager,
    DodoSecurityManager,
    FAB_AUTH_USER_REGISTRATION_ROLE,
)
from .google import GOOGLE_OAUTH_PROVIDER

AUTH_TYPE = AUTH_OAUTH
OAUTH_PROVIDERS = [DODO_OAUTH_PROVIDER_YANDEX, DODO_OAUTH_PROVIDER_AZURE]

# Add custom authorization via Dodo.Auth
CUSTOM_SECURITY_MANAGER = DodoSecurityManager

if is_development():
    OAUTH_PROVIDERS.append(GOOGLE_OAUTH_PROVIDER)
    OAUTH_PROVIDERS.remove(DODO_OAUTH_PROVIDER_AZURE)
    OAUTH_PROVIDERS.remove(DODO_OAUTH_PROVIDER_YANDEX)
    CUSTOM_SECURITY_MANAGER = DodoDevSecurityManager

# Map Authlib roles to superset roles
AUTH_ROLE_ADMIN = "Admin"
AUTH_ROLE_PUBLIC = FAB_AUTH_USER_REGISTRATION_ROLE

# Will allow user self registration, allowing to create Flask users from Authorized User
AUTH_USER_REGISTRATION = True

# The default user self registration role
AUTH_USER_REGISTRATION_ROLE = FAB_AUTH_USER_REGISTRATION_ROLE

# JWT
JWT_SECRET_KEY = get_env_variable("JWT_SECRET_KEY")
JWT_IDENTITY_CLAIM = "uid"
