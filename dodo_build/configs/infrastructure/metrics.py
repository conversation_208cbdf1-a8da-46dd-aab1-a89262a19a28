import json
import logging
from typing import Any

from flask import current_app
from prometheus_client import (
    CollectorRegistry,
    CONTENT_TYPE_LATEST,
    generate_latest,
    multiprocess,
)
from sqlalchemy.exc import SQLAlchemyError
from utils import send2kafka
from werkzeug.middleware.dispatcher import DispatcherMiddleware

from superset.prometheus_stats_logger import PrometheusStatsLogger
from superset.utils.log import AbstractEventLogger

logger = logging.getLogger(__name__)


class DashboardActivityLogger(AbstractEventLogger):
    """Event logger with user activity that commits logs to Superset DB"""

    def log(  # pylint: disable=too-many-arguments
        self,
        user_id: int | None,
        action: str,
        dashboard_id: int | None,
        duration_ms: int | None,
        slice_id: int | None,
        referrer: str | None,
        curated_payload: dict[str, Any] | None,
        curated_form_data: dict[str, Any] | None,
        *args: Any,
        **kwargs: Any,
    ) -> None:
        # pylint: disable=import-outside-toplevel
        from superset import db
        from superset.models.core import Log

        records = kwargs.get("records", [])
        logs = []

        for record in records:
            json_string: str | None = None
            try:
                json_string = json.dumps(record)
            except Exception as e:
                logger.warning(
                    f"[metrics.py] Failed to serialize record to JSON: {e}. Record: {record}"
                )

            is_plugin = (
                "https://officemanager" in referrer
                if referrer and isinstance(referrer, str)
                else False
            )

            log = Log(
                action=action,
                json=json_string,
                dashboard_id=dashboard_id,
                slice_id=slice_id,
                duration_ms=duration_ms,
                referrer=referrer,
                user_id=user_id,
                is_plugin=is_plugin,
            )
            logs.append(log)

            # Used in PrometheusStatsLogger
            if dashboard_id is not None:
                self.stats_logger.incr(f"dashboard_{dashboard_id}")

            try:
                # Used in PrometheusStatsLogger
                if duration_ms is not None and dashboard_id is not None:
                    self.stats_logger.duration(
                        dashboard_id=dashboard_id,
                        is_plugin=is_plugin,
                        duration_ms=duration_ms,
                    )

            except Exception as e:
                logger.error(f"[metrics.py] Failed to log to Prometheus: {e}")

        # Persist logs to database and send to Kafka
        try:
            db.session.bulk_save_objects(logs)
            db.session.commit()  # pylint: disable=consider-using-transaction
            send2kafka(db.session, len(logs))
        except SQLAlchemyError as ex:
            logger.error(
                "[metrics.py] DashboardActivityLogger failed to log event(s): %s",
                str(ex),
                exc_info=True,
            )

    @property
    def stats_logger(self) -> PrometheusStatsLogger:
        return current_app.config["STATS_LOGGER"]


def metrics_middleware(app):
    middleware = DispatcherMiddleware(app, {"/metrics": _make_multiprocess_wsgi_app()})
    return middleware


def _make_multiprocess_wsgi_app():
    def prometheus_app(environ, start_response):
        registry = CollectorRegistry()
        multiprocess.MultiProcessCollector(registry)
        data = generate_latest(registry)
        status = "200 OK"
        response_headers = [
            ("Content-type", CONTENT_TYPE_LATEST),
            ("Content-Length", str(len(data))),
        ]
        start_response(status, response_headers)
        return iter([data])

    return prometheus_app
