import logging
from datetime import datetime

import flask.config
from pythonjsonlogger import jsonlogger

from superset.utils.logging_configurator import LoggingConfigurator


class StructuredLogFormatter(jsonlogger.JsonFormatter):
    def add_fields(self, log_record, record, message_dict):
        super(StructuredLogFormatter, self).add_fields(log_record, record, message_dict)

        if not log_record.get("timestamp"):
            now = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            log_record["timestamp"] = now

        if log_record.get("level"):
            log_record["level"] = log_record["level"].upper()
        else:
            log_record["level"] = record.levelname


class DodoLoggingConfigurator(LoggingConfigurator):
    def configure_logging(
        self, app_config: flask.config.Config, debug_mode: bool
    ) -> None:
        log_level = "DEBUG" if debug_mode else "INFO"

        structured_handler = logging.StreamHandler()
        structured_handler.setFormatter(
            StructuredLogFormatter("%(timestamp)s %(level)s %(name)s %(message)s")
        )

        logging.basicConfig(
            handlers=[structured_handler],
            level=log_level,
        )
        logging.captureWarnings(True)

        root_logger = logging.getLogger()
        for _, logger in root_logger.manager.loggerDict.items():
            logger.handlers = []
            logger.propagate = True

        logging.getLogger("gunicorn.access").setLevel(logging.WARNING)

        if app_config["SILENCE_FAB"]:
            logging.getLogger("flask_appbuilder").setLevel(logging.ERROR)
