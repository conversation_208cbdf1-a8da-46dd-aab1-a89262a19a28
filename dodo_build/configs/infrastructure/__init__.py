from .cache import (
    CACHE_CONFIG,
    DATA_CACHE_CONFIG,
    GLO<PERSON>L_ASYNC_QUERIES_REDIS_CONFIG,
    RESULTS_BACKEND,
)
from .logging import DodoLoggingConfigurator
from .metrics import DashboardActivityLogger, metrics_middleware, PrometheusStatsLogger
from .worker import CeleryConfig

CELERY_CONFIG = CeleryConfig

LOGGING_CONFIGURATOR = DodoLoggingConfigurator()

STATS_LOGGER = PrometheusStatsLogger()
EVENT_LOGGER = DashboardActivityLogger()

ADDITIONAL_MIDDLEWARE = [
    metrics_middleware,
]
