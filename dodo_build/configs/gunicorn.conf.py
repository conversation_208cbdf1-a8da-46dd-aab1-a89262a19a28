accesslog = "-"  # Log access logs to stdout
errorlog = "-"  # Log error logs to stdout
workers = 7  # Adjust based on CPU cores: 2 * cores + 1
worker_class = "gevent"  # Use gevent for asynchronous workers
worker_connections = 1000  # Number of simultaneous connections per worker
timeout = 120  # Timeout for requests
limit_request_line = 0  # Disable limit on request line length
limit_request_field_size = 0  # Disable limit on header size
keepalive = 2  # Keep connection alive for 2 seconds

logger_class = "gunicorn_infrastructure.prometheus.GunicornPrometheusLogger"  # Custom logger for Prometheus metrics
statsd_prefix = "superset_gunicorn"  # Prefix for StatsD metrics

try:
    from prometheus_client import multiprocess

    def child_exit(server, worker):
        multiprocess.mark_process_dead(worker.pid)

except Exception:
    pass
