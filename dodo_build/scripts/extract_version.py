#!/usr/bin/env python3
"""
<PERSON>ript to extract VERSION from version.py without importing the entire module.
This avoids potential import errors from dependencies that might not be available during build.
"""

import os
import re
import sys


def extract_version(version_file_path):
    """
    Extract VERSION from version.py using regex parsing.

    Args:
        version_file_path (str): Path to the version.py file

    Returns:
        str: The VERSION value

    Raises:
        ValueError: If VERSION is not found in the file
        FileNotFoundError: If the version file doesn't exist
    """
    if not os.path.exists(version_file_path):
        raise FileNotFoundError(f"Version file not found: {version_file_path}")

    with open(version_file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # Look for VERSION = "value" or VERSION = 'value'
    pattern = r'^VERSION\s*=\s*["\']([^"\']+)["\']'
    match = re.search(pattern, content, re.MULTILINE)

    if match:
        return match.group(1)

    # Also try to match numeric values without quotes
    pattern_numeric = r"^VERSION\s*=\s*(\d+)"
    match_numeric = re.search(pattern_numeric, content, re.MULTILINE)

    if match_numeric:
        return match_numeric.group(1)

    raise ValueError("VERSION not found in the config file")


def main():
    """Main function to extract and print VERSION."""
    if len(sys.argv) != 2:
        print("Usage: python extract_version.py <path_to_version.py>", file=sys.stderr)
        sys.exit(1)

    config_file_path = sys.argv[1]

    try:
        version = extract_version(config_file_path)
        print(version)
    except (FileNotFoundError, ValueError) as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
