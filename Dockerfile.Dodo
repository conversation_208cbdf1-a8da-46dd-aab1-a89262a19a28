FROM acrdodo.azurecr.io/superset-frontend:latest AS frontend

FROM apache/superset:4.1.1

USER root

# Install requirements
COPY dodo_build/requirements.txt /app/requirements/
RUN pip install --no-cache-dir --upgrade uv
RUN --mount=type=cache,target=/root/.cache/uv \
    apt-get update -qq && apt-get install -yqq --no-install-recommends \
      build-essential pkg-config \
    && uv pip install --system -r /app/requirements/requirements.txt \
    && apt-get autoremove -yqq --purge build-essential pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Add configuration
COPY dodo_build/configs/ /app/configs/

# Add bootstrap commands
COPY dodo_build/docker /usr/bin/

# Add our superset code
COPY superset/ /app/superset/

RUN rm -rf /app/superset/static/assets/
RUN rm -rf /app/superset-frontend/

COPY --from=frontend /app/superset/static/assets/ /app/superset/static/assets/

USER superset
